import { generateTransformerCaller } from './base-caller'
import path from 'path'
import fs from 'fs'

const { Command } = require('commander')
const testProgram = new Command()

testProgram
  .option('-t, --target <string>', '输入文件')
  .option('-m, --mode <string>', '模式', '')

testProgram.parse(process.argv)

const testOptions = {
    file: String(testProgram.opts().target ?? ''),
    model: String(testProgram.opts().mode === 'gpt4' ? 'apilearn4' : 'apilearn')
} as const

function syncFileSplitter() {
    if (!testOptions.file) {
        console.log('❌ 请提供要转换的文件或文件夹，例如：`mj -t index.vue`')
        throw new Error('没有提供转换的文件')
    }
    const files = testOptions.file.split(',')
    const resolvePaths = files.map(f => path.resolve(process.env.INIT_CWD ?? '', f))

    const result = resolvePaths.map(rp => {
        if (fs.lstatSync(rp).isDirectory()) {
            // dir solution
            const ff = fs.readdirSync(rp)
            return ff.map(t => path.resolve(rp, t)).filter(t => t.endsWith('.js') || t.endsWith('.ts') || t.endsWith('.json'))
        }
        return [rp].filter(t => t.endsWith('.js') || t.endsWith('.ts') || t.endsWith('.json'))
    }).flat()
    return result
}


function createExecutors() {
    const files = syncFileSplitter()
    if (!files.length) {
        throw new Error('没有找到 API 文件') 
    }
    return files.map(f => {
        const fileContent = fs.readFileSync(f, 'utf-8')
        return () => generateTransformerCaller(
            `https://s3plus.meituan.net/v1/mss_12da94d5231641f491206dfcfc1028a5/dyna/213/dist/${testOptions.model}/latest.json`,
            () => {
                const fileType = f.endsWith('.ts')
                    ?'typescript'
                    : f.endsWith('json') 
                        ? 'json'
                        : 'javascript'
                const prompt = `我的文件如下：\n\`\`\`${fileType}\n${fileContent}\n \`\`\`\n`
                return Promise.resolve(prompt)
            },
            (rawResult) => {
                if (rawResult.error) {
                  console.log('Error at: ', f, rawResult.error)
                  return Promise.resolve({ f, data: '', error: rawResult.error })
                }
                // @ts-ignore 处理结果，两部分代码组成，第一部分是服务，第二部分是组件
                const target = String(rawResult.answer).split('\n')
                const vueBlock = `\`\`\`md`
                const vueEndBlock = `\`\`\``
                const templatePosition = target.indexOf(vueBlock)
                const templateEndPosition = target.indexOf(vueEndBlock, templatePosition + 1)
                const template = templatePosition !== -1 && templateEndPosition !== -1 && templateEndPosition > templatePosition
                    ? target.slice(templatePosition + 1, templateEndPosition).join('\n')
                    : target
                console.log(`🍻 ${f} 生成成功！`)
                return Promise.resolve({ f, error: null, data:template })
            },
            '123'
        )
    })
}

async function doStuff() {
    const executors = createExecutors()
    const resultPath = path.resolve(process.env.INIT_CWD ?? '', 'api-result.md')
    const rawPath = path.resolve(process.env.INIT_CWD ?? '', 'api-raw.md')
    const resultList = [] as string[]
    const rawList = {} as Record<string, any>
    await executors.reduce((prev, next) => {
        return prev.then(() => next().then(data => {
            resultList.push(data.result.data)
            rawList[data.result.f] = data.rawResult
            return data
        }))
    }, Promise.resolve({}))

    fs.writeFileSync(resultPath, resultList.join('\n'))
    console.log(`🍻 统计文件已输出到：${resultPath}`)
    fs.writeFileSync(rawPath, JSON.stringify(rawList))
    console.log(`🍻 原始文件已输出到：${rawPath}`)
    console.log(`✅ 全部完成！`)
}

doStuff()