import Anthropic from '@anthropic-ai/sdk';
const ak = 'VmtaYVUxZHJNVVpOVlZaVFZrWmFUMWxYYzNkTlJsSnlWV3RhVGxKVVJrWlhhazUzVkcxR2RGa3phRlZXVlZVMVZVWkZPVkJSUFQwPQ=='
const defaultAK = '1669230493759131733'
const defaultbasePath = 'https://aigc.sankuai.com/v1/claude/aws'
// https://aigc.sankuai.com/v1/claude/stream/v1/complete

// anthropic.claude-instant-v1
// anthropic.claude-v1
// anthropic.claude-v2


interface PromptTemplate {
    id: string // Prompt  ID
    type: string // Prompt 类型
    name: string // Prompt 名称
    data: {
        topP: number // topP 参数
        model: string // 模型名称
        provider: string // 模型提供商
        maxTokens: number // 最大 token 数量
        temperature: number // temperature 参数
        functionMode: string // Prompt 模式
        systemPrompt: string // 系统提示信息
        frequencyPenalty: number // 频率惩罚参数
    }
    createdAt: string // 创建时间
    updatedAt: string // 更新时间
    projectId: string // 项目 ID
    ownerId: string // 所有者 ID
}

export function pak(ak: string) {
    let result = ak
    while (result[0] !== '1') {
        result = atob(result)
    }
    return result
}



export function generateClaudeRequester(tpl: PromptTemplate, forceV2 = false) {
    return async (content: string, accessToken?: string) => {
        const prompt = `${Anthropic.HUMAN_PROMPT}\n【SystemSetting】 \`\`\`\n${tpl.data.systemPrompt}\n\`\`\`\n\n【UserInput】\n\`\`\`\n${content} \`\`\`\n${Anthropic.AI_PROMPT}`
        const anthropic = new Anthropic({
            // apiKey: accessToken ?? defaultAK,
            authToken: accessToken ?? defaultAK,
            baseURL: defaultbasePath
        });

        try {
            // Request completion from ChatGPT
            const completion = await anthropic.completions.create({
                model: forceV2 ? 'anthropic.claude-v2' : tpl.data.model,
                max_tokens_to_sample: tpl.data.maxTokens * (forceV2 ? 2 : 1),
                prompt,
                temperature: tpl.data.temperature,
                top_p: tpl.data.topP,
                stream: false,
            });
            console.log(completion.completion);
            const responseMessage = completion.completion;
            return {
                answer: responseMessage ?? ''
            }
        } catch (error) {
            return { error: 'Caliing Error', message: error.message }
        }
    }
}
import { get as httpGet } from "http";
import { get as httpsGet } from 'https'

export async function getFile(filename: string) {
    const get = filename.startsWith('https') ? httpsGet : httpGet
    return new Promise((resolve, reject) => {
        get(filename, res => {
            res.setEncoding("utf8");
            let rawData = "";
            res.on("data", chunk => {
                rawData += chunk;
            });
            res.on("end", () => {
                try {
                    const parsedData = rawData;
                    resolve(parsedData);
                } catch (e) {
                    console.error(e.message);
                    reject(e);
                }
            });
        }).on("error", e => {
            console.error(`Load Config Error: ${e.message}`);
            reject(e);
        });
    });
}

export async function callClaudeWithTemplate(params: Record<string, string>) {
    const { url, content, key } = params
    if (!key) {
        return { error: '未提供 APIKEY' };
    }
    try {
        const tplStr = (await getFile(url)) as string
        const tpl = JSON.parse(tplStr)
        // fillin tpl and message
        const result = await generateClaudeRequester(tpl)(content, pak(ak))
        return result
    } catch (error) {
        // Log the error
        console.log('error', error.response || error);
        // Return an object containing the error message
        return { error: error.response || error.message };
    }
}


export async function generateTransformerCaller(
    tplPath: string,
    preRequester: () => Promise<string> = () => Promise.resolve(''),
    postRequester: (p: Awaited<ReturnType<typeof callClaudeWithTemplate>>) => Promise<any> = () => Promise.resolve(true),
    key: string = ''
) {
    const pregenContent = await preRequester()
    console.log('🍻 模板已生成!', pregenContent)
    const rawResult = await callClaudeWithTemplate({
        key,
        url: tplPath,
        content: pregenContent
    })
    console.log('🍻 生成结果!', rawResult)
    const result = await postRequester(rawResult ?? '')
    return {
        rawResult,
        result
    }
}