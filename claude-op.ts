import { generateTransformerCaller } from './base-caller-claude'

import path from 'path'
import fs from 'fs'
const testFile = require('./test-generate.json')
const { Command } = require('commander')
const testProgram = new Command()

// testProgram
//   .option('-t, --target <string>', '输入文件')

testProgram.parse(process.argv)

const testOptions = {
    file: String(testProgram.opts().target ?? ''),
} as const

function transformer() {
    // if (!testOptions.file) {
    //     console.log('❌ 请提供要转换的文件，例如：`yarn smc fdo -t index.vue`')
    //     console.log('👉 如果需要从 VCA 转换，可以直接使用 `yarn smc fdo -t index.vue`')
    //     throw new Error('没有提供转换的文件')
    // }
    // const filePath = path.resolve(process.env.INIT_CWD ?? '', testOptions.file)
    // const fileContent = fs.readFileSync(filePath, 'utf-8')
    // const prompt = `我的组件如下：\n\`\`\`vue\n${fileContent}\n \`\`\`\n`
    return Promise.resolve(testFile.content)
}

function extracter(rawResult: any) {
     // 处理结果，两部分代码组成，第一部分是服务，第二部分是组件
    //  const templateName = `${testOptions.file.replace('.vue', '')}.vca.vue`
    //  const templatePath = path.resolve(process.env.INIT_CWD ?? '', templateName)
 
    //  // 处理
    //  if (rawResult.error) {
    //    throw rawResult.error
    //  }
 
    //  // handle Result, first is api.types.ts, second is api.ts
    //  const target = String(rawResult.answer).split('\n')
 
    //  const vueBlock = `\`\`\`vue`
    //  const vueEndBlock = `\`\`\``
 
    //  const templatePosition = target.indexOf(vueBlock)
    //  const templateEndPosition = target.indexOf(vueEndBlock, templatePosition + 1)
 
    //  const template = templatePosition !== -1 && templateEndPosition !== -1 && templateEndPosition > templatePosition
    //    ? target.slice(templatePosition + 1, templateEndPosition).join('\n')
    //    : ''
    //  console.log('🍻 恭喜生成成功！')
    //  fs.writeFileSync(templatePath, template)
    //  console.log(`🍻 模板已输出到：${templatePath}`)
    //  console.log(`✅ 全部完成！`)
    console.log('生成的摘要信息：', rawResult)
    fs.writeFileSync('summary_claude.json', JSON.stringify({ generated: rawResult }), 'utf-8')
    return Promise.resolve(true)
}

generateTransformerCaller(
    "https://s3plus.meituan.net/v1/mss_12da94d5231641f491206dfcfc1028a5/dyna/213/dist/claude_brief/latest.json",
    transformer,
    extracter,
    '123'
)