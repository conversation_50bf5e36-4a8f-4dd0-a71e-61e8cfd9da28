const mammoth = require("mammoth");
const fs = require("fs");
const path = require("path");

// 示例转换：将指定的 Word 文档转换为 HTML
async function convertExample() {
    const inputPath = "/Users/<USER>/Documents/hlsop/保安部 SOP/SEC SOP/SOP001.doc";
    const outputPath = "/Users/<USER>/Desktop/hlsop/保安部 SOP/SEC SOP/SOP001.html";
    
    try {
        console.log("开始转换文档...");
        console.log(`输入: ${inputPath}`);
        console.log(`输出: ${outputPath}`);
        
        // 检查输入文件是否存在
        if (!fs.existsSync(inputPath)) {
            console.error(`❌ 输入文件不存在: ${inputPath}`);
            return;
        }
        
        // 确保输出目录存在
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
            console.log(`✅ 创建输出目录: ${outputDir}`);
        }
        
        // 使用 mammoth 转换文档
        const result = await mammoth.convertToHtml({ path: inputPath });
        
        const html = result.value; // 生成的 HTML
        const messages = result.messages; // 转换过程中的消息
        
        // 写入 HTML 文件
        fs.writeFileSync(outputPath, html, 'utf8');
        
        console.log("✅ 转换成功！");
        console.log(`📄 HTML 文件已保存到: ${outputPath}`);
        
        // 显示转换消息
        if (messages.length > 0) {
            console.log("\n转换消息:");
            messages.forEach(message => {
                console.log(`${message.type}: ${message.message}`);
            });
        }
        
    } catch (error) {
        console.error(`❌ 转换失败: ${error.message}`);
    }
}

// 运行转换
convertExample();
