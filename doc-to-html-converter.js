const mammoth = require("mammoth");
const fs = require("fs");
const path = require("path");

/**
 * 将 Word 文档转换为 HTML
 * @param {string} inputPath - 输入文档路径
 * @param {string} outputPath - 输出 HTML 路径
 */
async function convertDocToHtml(inputPath, outputPath) {
    try {
        console.log(`开始转换文档: ${inputPath}`);
        
        // 检查输入文件是否存在
        if (!fs.existsSync(inputPath)) {
            throw new Error(`输入文件不存在: ${inputPath}`);
        }
        
        // 确保输出目录存在
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
            console.log(`创建输出目录: ${outputDir}`);
        }
        
        // 使用 mammoth 转换文档
        const result = await mammoth.convertToHtml({ path: inputPath });
        
        const html = result.value; // 生成的 HTML
        const messages = result.messages; // 转换过程中的消息，如警告
        
        // 创建完整的 HTML 文档
        const fullHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${path.basename(inputPath, path.extname(inputPath))}</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .content {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3, h4, h5, h6 {
            color: #333;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        p {
            margin-bottom: 1em;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="content">
        ${html}
    </div>
</body>
</html>`;
        
        // 写入 HTML 文件
        fs.writeFileSync(outputPath, fullHtml, 'utf8');
        
        console.log(`✅ 转换成功！`);
        console.log(`📄 输入文件: ${inputPath}`);
        console.log(`📄 输出文件: ${outputPath}`);
        
        // 显示转换过程中的消息
        if (messages.length > 0) {
            console.log('\n⚠️  转换消息:');
            messages.forEach(message => {
                console.log(`   ${message.type}: ${message.message}`);
            });
        }
        
        return { success: true, outputPath, messages };
        
    } catch (error) {
        console.error(`❌ 转换失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 批量转换函数
 * @param {string} sourceDir - 源目录
 * @param {string} targetDir - 目标目录
 * @param {string} filePattern - 文件模式（可选）
 */
async function batchConvert(sourceDir, targetDir, filePattern = '**/*.{doc,docx}') {
    const glob = require('glob');
    
    try {
        const files = glob.sync(filePattern, { cwd: sourceDir });
        console.log(`找到 ${files.length} 个文档文件`);
        
        for (const file of files) {
            const inputPath = path.join(sourceDir, file);
            const outputPath = path.join(targetDir, file.replace(/\.(doc|docx)$/i, '.html'));
            
            await convertDocToHtml(inputPath, outputPath);
        }
        
    } catch (error) {
        console.error(`批量转换失败: ${error.message}`);
    }
}

// 主程序
async function main() {
    // 示例文件路径
    const inputPath = "/Users/<USER>/Documents/hlsop/保安部 SOP/SEC SOP/SOP001.doc";
    const outputPath = "/Users/<USER>/Desktop/hlsop/保安部 SOP/SEC SOP/SOP001.html";
    
    console.log("🚀 Word 文档转 HTML 工具");
    console.log("========================");
    
    // 转换示例文件
    const result = await convertDocToHtml(inputPath, outputPath);
    
    if (result.success) {
        console.log("\n🎉 转换完成！你可以在浏览器中打开 HTML 文件查看结果。");
    }
}

// 如果直接运行此脚本，执行主程序
if (require.main === module) {
    main().catch(console.error);
}

// 导出函数供其他模块使用
module.exports = {
    convertDocToHtml,
    batchConvert
};
