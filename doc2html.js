#!/usr/bin/env node

const mammoth = require("mammoth");
const fs = require("fs");
const path = require("path");

/**
 * 显示使用帮助
 */
function showHelp() {
    console.log(`
Word 文档转 HTML 工具

使用方法:
  node doc2html.js <输入文件路径> [输出文件路径]

参数:
  输入文件路径    Word 文档路径 (.doc 或 .docx)
  输出文件路径    HTML 输出路径 (可选，默认与输入文件同目录)

示例:
  node doc2html.js "/path/to/document.doc"
  node doc2html.js "/path/to/document.doc" "/path/to/output.html"
  node doc2html.js "SOP001.doc" "SOP001.html"

特殊功能:
  node doc2html.js --example    运行示例转换
`);
}

/**
 * 转换 Word 文档为 HTML
 */
async function convertDocToHtml(inputPath, outputPath) {
    try {
        console.log(`🔄 开始转换: ${path.basename(inputPath)}`);
        
        // 检查输入文件
        if (!fs.existsSync(inputPath)) {
            throw new Error(`输入文件不存在: ${inputPath}`);
        }
        
        // 确保输出目录存在
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
            console.log(`📁 创建目录: ${outputDir}`);
        }
        
        // 转换文档
        const result = await mammoth.convertToHtml({ path: inputPath });
        
        // 创建完整的 HTML 文档
        const fullHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${path.basename(inputPath, path.extname(inputPath))}</title>
    <style>
        body { 
            font-family: "Microsoft YaHei", Arial, sans-serif; 
            line-height: 1.6; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        h1, h2, h3, h4, h5, h6 { color: #333; margin-top: 1.5em; }
        table { border-collapse: collapse; width: 100%; margin: 1em 0; }
        table, th, td { border: 1px solid #ddd; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    ${result.value}
</body>
</html>`;
        
        // 写入文件
        fs.writeFileSync(outputPath, fullHtml, 'utf8');
        
        console.log(`✅ 转换成功!`);
        console.log(`📄 输出文件: ${outputPath}`);
        
        // 显示消息
        if (result.messages.length > 0) {
            console.log(`\n⚠️  转换消息 (${result.messages.length} 条):`);
            result.messages.forEach(msg => {
                console.log(`   ${msg.type}: ${msg.message}`);
            });
        }
        
        return true;
        
    } catch (error) {
        console.error(`❌ 转换失败: ${error.message}`);
        return false;
    }
}

/**
 * 运行示例转换
 */
async function runExample() {
    const inputPath = "/Users/<USER>/Documents/hlsop/保安部 SOP/SEC SOP/SOP001.doc";
    const outputPath = "/Users/<USER>/Desktop/hlsop/保安部 SOP/SEC SOP/SOP001.html";
    
    console.log("🚀 运行示例转换");
    console.log("================");
    
    return await convertDocToHtml(inputPath, outputPath);
}

/**
 * 主程序
 */
async function main() {
    const args = process.argv.slice(2);
    
    // 显示帮助
    if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
        showHelp();
        return;
    }
    
    // 运行示例
    if (args.includes('--example')) {
        await runExample();
        return;
    }
    
    // 获取输入和输出路径
    const inputPath = args[0];
    let outputPath = args[1];
    
    // 如果没有指定输出路径，自动生成
    if (!outputPath) {
        const dir = path.dirname(inputPath);
        const name = path.basename(inputPath, path.extname(inputPath));
        outputPath = path.join(dir, name + '.html');
    }
    
    // 转换文档
    const success = await convertDocToHtml(inputPath, outputPath);
    
    if (success) {
        console.log("\n🎉 转换完成！可以在浏览器中打开 HTML 文件查看结果。");
    }
}

// 运行主程序
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { convertDocToHtml, runExample };
