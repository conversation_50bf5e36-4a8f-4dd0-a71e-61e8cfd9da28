## 账号管理

- 【获取账号信息】： GET /api/agent/v1/info/account/child
- 【编辑账号】： POST /api/agent/v1/info/account/edit
- 【搜索子账号】： GET /api/agent/v1/info/account/searchchild
- 【为账号绑定 POI】： POST /api/agent/v1/info/account/bindpoi
- 【为账号解绑 POI】： POST /api/agent/v1/info/account/unbindpoi
- 【获取权限信息】： GET /api/agent/v1/info/account/selectedmenu
- 【搜索账号 POI】： GET /api/agent/v1/info/account/searchpoi

## 通用能力

- 【获取系统菜单】： GET /api/agent/v1/common/menu
- 【获取账号基本信息】： GET /api/agent/v1/common/basicinfo
- 【获取未处理消息】： GET /api/agent/v1/common/msg/unhandled
- 【获取发票统计】： GET /api/agent/v1/finance/{partnerId}/stateInvoiceCount
- 【获取美团发票助手注册信息】： GET /api/agent/v1/finance/invoice/signUp/info
- 【获取美团发票助手安装包下载地址】： GET https://invoicemerchant.meituan.com/register/downNewExe
- 【上传文件】： POST /api/agent/v1/attachment/{type}/uploadFile
- 【获取触达平台推送的消息】： POST /api/v1/touch/msg/pull/page
- 【点击消息】： POST /api/v1/touch/msg/update/open
- 【通用文件上传】： POST /gw/file/upload/common
- 【获取 DOS 回溯权益信息】： GET /api/agent/v1/dos/score/backRightsInfo
- 【查询 MTA 名单库】： GET /api/agent/v1/meta/search/exist
- 【触达公告已读数据上报】： POST /api/agent/v1/notice/readReport
- 【获取订单列表】： GET /api/agent/v1/order/apt/orders
- 【获取订单可选 POI 列表】： GET /api/agent/v1/order/apt/pois
- 【获取账号下可选房型信息】： GET /api/agent/v1/order/apt/getRoomType
- 【获取取消订单数量】： GET /api/agent/v1/order/apt/notice/list
- 【获取退款订单数量】： GET /api/agent/v1/order/apt/refund/count
- 【发送取消订单通知】： POST /api/agent/v1/order/apt/notice/order/{orderId}
- 【发送退款订单通知】： POST /api/agent/v1/order/apt/refund/order/{orderId}
- 【订单操作】： POST /api/agent/v1/order/apt/status/op
- 【获取订单数量】： GET /api/agent/v1/order/apt/count
- 【获取自动接单数量】： GET /api/agent/v1/order/auto/count
- 【确认自动接单订单】： POST /api/agent/v1/ebooking/order/confirmNotice
- 【获取订单详情】： GET /api/agent/v1/order/apt/detail
- 【获取订单日志】： GET /api/agent/v1/order/apt/logs
- 【查询当前门店是否有预付 POI】： GET /api/agent/v1/order/apt/hasPrepayPoi
- 【修改确认号】： POST /api/agent/v1/order/apt/{orderId}/confirmationNumber
- 【获取确认号填写条件】： GET /api/agent/v1/order/apt/{orderId}/confirmationNumber/condition
- 【获取可转售订单数量】： GET /api/agent/v1/order/apt/zf/resale/count
- 【确认转售订单】： POST /api/agent/v1/order/apt/zf/resale/{orderId}/bizConfirm
- 【获取转售订单列表】： GET /api/agent/v1/order/apt/zf/resale/{orderId}/oldOrderInfo
- 【获取养蜂计划订单数量】： GET /api/agent/v1/order/apt/bee/count
- 【确认养蜂计划订单】： POST /api/agent/v1/order/apt/bee/bizConfirm/all/{status}
- 【获取养蜂计划订单日志】： GET /api/agent/v1/order/apt/bee/{orderId}/logs
- 【获取订单联系人虚拟号码】： GET /api/agent/v1/order/apt/getVirtualNumber
- 【获取订单联系人姓名】： GET /api/agent/v1/order/apt/{orderId}/userInfo
- 【校验源订单确认号是否重复】： POST /api/agent/v2/others/checkSourceOrderId
- 【提前入住查询】： GET /api/agent/v1/orders/{orderId}/earlyCheckIn
- 【查询订单权益信息】： POST /api/agent/v2/orders/rights/select
- 【接受订单权益】： POST /api/agent/v2/orders/rights/accept
- 【保存权益库存-会员团队提供】： POST /api/agent/v2/rights/editStockOnDate
- 【获取协议已签约的 POI 列表】： GET /api/agent/v1/protocol/dealDetail/poiList
- 【协议签约】： POST /api/agent/v1/protocol/deal
- 【获取未签约协议详情】： GET /api/agent/v1/protocol/unsigned/detail
- 【获取所有任务】： GET /api/agent/v1/task/getAllTasks
- 【检查签约协议】： GET /api/agent/v1/protocol/checkSignAgreement
- 【服务看板搜索】： GET /api/agent/v1/kanban/search
- 【服务看板-不符合数量】： GET /api/agent/v1/kanban/qualified
- 【系统退出登录】： POST /api/agent/v1/sso/logout
- 【获取酒店基本信息】： GET /api/agent/v1/vpoi/basic/poiId{/poiId}/sim/basicInfo
- 【更新酒店服务时间】： POST /api/agent/v1/vpoi/basic/partnerId{/partnerId}/pois/serviceTime/sync
- 【检查酒店头图】： GET /api/agent/v1/vpoi/goods/poiId{/poiId}/checkFrontImg
- 【检查 SPU 是否有权限】： GET /api/agent/v1/prepay/partner/{partnerId}/poi/{poiId}/spu/hasAuthority
- 【获取 CRS 白名单】： POST /api/agent/v1/prepay/meta/searchItemExist

## DOS

- 【获取月度得分】： GET /api/agent/v1/dos/score/bymonth
- 【获取得分趋势】： GET /api/agent/v1/dos/score/historyTrend
- 【获取得分详情】： GET /api/agent/v1/dos/score/historyDetail

## 商家钱包

- 【获取钱包信息】： GET /api/agent/v1/finance/getwalletInfo
- 【绑定钱包】： GET /api/agent/v1/finance/bindWallet
- 【获取账号结算方式】： GET /api/agent/v1/finance/financeTransInfo
- 【获取门店列表】： GET /api/agent/v1/vpoi/info/searchpoi
- 【获取钱包信息（供应商结算方式）】： GET /api/agent/v1/finance/partner/walletInfo
- 【获取钱包信息（门店结算方式）】： GET /api/agent/v1/finance/poi/{pId}/walletInfo

## 订单 （HB 待补充）

- 【上报竞对信息】： POST /api/agent/v1/order/competitor/report
- 【批量上报竞对信息】： POST /api/agent/v1/order/competitor/batchreport
- 【获取上报历史列表】： GET /api/agent/v1/order/competitor/reportHistory

## 门店管理

- 【搜索异常商品】： GET /api/agent/v1/vpoi/abnormal/goods/search
- 【获取异常商品日历】： GET /api/agent/v1/vpoi/abnormal/goods/calendar
- 【批量确认异常商品】： POST /api/agent/v1/vpoi/abnormal/goods/batchconfirm
- 【获取异常商品分类】： GET /api/agent/v1/vpoi/abnormal/goods/categories
- 【获取门店未供货产品列表】： GET /api/agent/v1/vpoi/unsupply/list
- 【确认门店供货产品】： POST /api/agent/v1/vpoi/unsupply/confirm
- 【门店搜索】： GET /api/agent/v1/vpoi/info/searchpoi
- 【获取门店信息】： GET /api/agent/v1/vpoi/info/poisim
- 【绑定门店】： POST /api/agent/v1/vpoi/info/bindpoi
- 【批量开通打包】： POST /api/agent/v1/vpoi/info/packageVas/status
- 【全部开通打包】： POST /api/agent/v1/vpoi/info/packageVas/status/all
- 【价格指导-门店搜索】： GET /api/agent/v1/vpoi/goods/priceRadar
- 【价格指导-忽略信息】： POST /api/agent/v1/vpoi/goods/priceRadarBatch
- 【价格指导-获取建议价日历】： POST /api/agent/v1/vpoi/goods/batchReducePrice
- 【价格指导-批量修改价格】： POST /api/agent/v1/vpoi/goods/batchUpdatePriceByGuide
- 【搜索门店信息】： GET /api/agent/v1/sc/gw/biz/vpois
- 【提交门店】： POST /api/agent/v1/sc/gw/biz/poi
- 【获取门店详情】： GET /api/agent/v1/sc/gw/biz/poi
- 【上传门店资质】： POST /api/agent/v1/sc/gw/biz/qualification/upload
- 【绑定POI】： POST /api/agent/v1/vpoi/info/bindpois/cluster
- 【忽略推荐】： POST /api/agent/v1/vpoi/info/ignoreRecommend
- 【获取推荐列表】： GET /api/agent/v1/vpoi/info/recommend
- 【获取城市列表】： GET /api/agent/v1/vpoi/info/getCityList
- 【获取区域信息】： GET /api/agent/v1/vpoi/info/getDistrictInfo
- 【查询门店基础信息】： GET /api/agent/v1/vpoi/basic/partnerId/{partnerId}/poiId/{poiId}/query
- 【更新门店基础信息】： POST /api/agent/v1/vpoi/basic/partnerId/{partnerId}/poiId/{poiId}/update
- 【门店搜索】： GET /api/agent/v1/vpoi/info/searchpoi

## 产品管理（老）

- 【检查 MTA 名单库】： POST /api/agent/v1/prepay/meta/searchItemExist
- 【获取订单变更商品列表（转售）】： GET /api/agent/v1/vpoi/goods/zf/orderGoodsList
- 【获取订单变更列表（转售）】： GET /api/agent/v1/vpoi/goods/{poiId}/zf/orderList
- 【获取礼包列表】： GET /api/agent/v1/vpoi/goods/{poiId}/gift/list
- 【更新礼包】： POST /api/agent/v1/vpoi/goods/{poiId}/gift/submit
- 【删除礼包】： POST /api/agent/v1/vpoi/goods/{poiId}/gift/{giftId}/delete
- 【获取礼包类型】： GET /api/agent/v1/vpoi/goods/{poiId}/gift/typeList
- 【获取产品列表】： GET /api/agent/v1/vpoi/goods/prepayGoodsList
- 【获取产品列表（直连）】： GET /api/agent/v1/vpoi/goods/zlGoodsList
- 【获取产品名称列表】： GET /api/agent/v1/vpoi/goods/poiId/{poiId}/goodsNames
- 【获取加价率】： GET /api/agent/v1/vpoi/goods/poiId/{poiId}/getPriceRate
- 【更新产品状态】： POST /api/agent/v1/vpoi/goods/onlineSwitch
- 【更新房态】： POST /api/agent/v1/vpoi/goods/poiId/{poiId}/roomSwitch
- 【作废产品】： POST /api/agent/v1/partner/{partnerId}/poi/{poiId}/goods/destroy/{preGoodsId}
- 【批量修改库存】： POST /api/agent/v1/vpoi/goods/poiId/{poiId}/batchUpdateInventory
- 【批量修改产品价格】： POST /api/agent/v1/vpoi/goods/poiId/{poiId}/batchUpdateGoodsPriceNew
- 【批量修改取消规则】： POST /api/agent/v1/vpoi/goods/poiId/{poiId}/batchUpdateCancelRules
- 【获取产品详情】： GET /api/agent/v1/vpoi/goods/{goodsId}/poiId/{poiId}
- 【更新产品详情】： POST /api/agent/v1/vpoi/goods/{goodsId}/poiId/{poiId}/update
- 【根据日期段查询产品价格】： POST /api/agent/v1/vpoi/goods/poiId/{poiId}/queryPriceByDate
- 【根据报价消息id获取价格日历】： GET /api/agent/v1/vpoi/goods/poiId/{poiId}/{messageId}/priceCalendar
- 【查询切换进度】： GET /api/agent/v1/vpoi/goods/getProcessRate/{uuid}
- 【查询改价进度】： POST /api/agent/v1/vpoi/goods/getProcessRate
- 【产品维度批量设置房态】： POST /api/agent/v1/vpoi/goods/poiId/{poiId}/roomBatchSwitch
- 【新增产品、新增价格】： POST /api/agent/v1/vpoi/goods/poiId/{poiId}/goods/batchGoodsCreate
- 【查询产品标签信息】： GET /api/agent/v1/vpoi/goods/poiId/{poiId}/queryGoodsTagsInfo/{pregoodsId}/{uuid}
- 【查询房型】： GET /api/agent/v1/vpoi/goods/poiId/{poiId}/room/goods
- 【分页查询房型】： GET /api/agent/v1/vpoi/goods/poiId/{poiId}/room/goods/page
- 【查询是否测试城市】： GET /api/agent/v1/vpoi/goods/checkpoi
- 【获取价格指导信息】： POST /api/agent/v1/vpoi/goods/poiId/{poiId}/competePriceByRoom
- 【输入底价获取计算好的卖价和佣金等等值】： POST /api/agent/v1/vpoi/goods/getPriceInfoByBaseAdvanceRatio
- 【计算建议底价】： GET /api/agent/v1/vpoi/goods/goodsCompetingPriceList
- 【计算建议底价（直连）】： GET /api/agent/v1/vpoi/goods/goodsCompetingPriceListForZl
- 【价格修改列表】： GET /api/agent/v1/vpoi/goods/partner/{partnerId}/poi/{poiId}/goods/{goodsId}/logs/{param}
- 【价格修改详情】： GET /api/agent/v1/vpoi/goods/partner/{partnerId}/poi/{poiId}/goods/{goodsId}/logs/{param}/detail
- 【房型维度查看库存修改记录】： GET /api/agent/v1/vpoi/goods/partner/{partnerId}/poi/{poiId}/getInventoryChangeLogs/{roomId}
- 【goods维度查看库存修改记录】： GET /api/agent/v1/vpoi/goods/room/status/change/log/query
- 【服务时间修改记录】： GET /api/agent/v1/sc/gw/vpoi/{partnerId}/{poiId}/servicetimeChange
- 【预留房保留时间修改记录】： GET /api/agent/v1/sc/gw/vpoi/{partnerId}/{poiId}/keeptimeChange
- 【价格模式修改记录】： GET /api/agent/v1/sc/gw/vpoi/priceModeRecord/query
- 【获取 SIM 信息】： GET /api/agent/v1/vpoi/basic/sim/basicInfo
- 【获取图片】： GET /api/agent/v1/vpoi/images
- 【设置预留房时间】： POST /api/agent/v1/vpoi/info/poiId/{poiId}/reservedKeepTime/keepTime
- 【获取列表】： GET /api/agent/v1/vpoi/trace/poiId/{poiId}/rules
- 【创建规则】： POST /api/agent/v1/vpoi/trace/poiId/{poiId}/rule/create
- 【编辑规则】： POST /api/agent/v1/vpoi/trace/poiId/{poiId}/rule/update
- 【批量关联】： POST /api/agent/v1/vpoi/trace/poiId/{poiId}/batchAttach
- 【批量解除关联】： POST /api/agent/v1/vpoi/trace/poiId/{poiId}/batchDetach
- 【获取建议】： GET /api/agent/v1/vpoi/trace/poiId/{poiId}/rule/price/suggest
- 【获取记录】： GET /api/agent/v1/vpoi/trace/poiId/{poiId}/rule/records

## 供应商管理

- 【获取列表】： GET /api/agent/v1/vpoi/immediate/confirm/searchpoi
- 【修改状态】： POST /api/agent/v1/vpoi/immediate/confirm/changeStatus
- 【获取历史记录】： GET /api/agent/v1/vpoi/immediate/confirm/record
- 【获取BD信息】： GET /api/agent/v1/info/partner/bd
- 【获取基础信息】： GET /api/agent/v1/info/partner/basic
- 【获取财务信息】： GET /api/agent/v1/info/partner/finance
- 【获取联系人信息】： GET /api/agent/v1/info/partner/contacts
- 【设置联系人信息】： POST /api/agent/v1/info/partner/contacts
- 【获取服务时间】： GET /api/agent/v1/info/partner/servicetime
- 【设置服务时间】： POST /api/agent/v1/info/partner/servicetime
- 【获取钱包信息】： GET /api/agent/v1/finance/bindWallet

## 违规违约

- 【获取违章数量】： GET /api/agent/v1/violation/query/count
- 【获取违章列表】： GET /api/agent/v1/violation/query/list
- 【获取违章操作日志】： GET /api/agent/v1/violation/query/oplog
- 【提交申诉】： POST /api/agent/v1/violation/appeal/submit
- 【已知违章】： POST /api/agent/v1/violation/known