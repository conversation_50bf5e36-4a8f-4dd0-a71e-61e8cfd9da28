import { Configuration, OpenAIApi } from 'openai'
const ak = 'VmtaYVUxZHJNVVpOVlZaVFZrWmFUMWxYYzNkTlJsSnlWV3RhVGxKVVJrWlhhazUzVkcxR2RGa3phRlZXVlZVMVZVWkZPVkJSUFQwPQ=='
const defaultAK = '1669230493759131733'
const defaultbasePath = 'https://aigc.sankuai.com/v1/openai/native'


interface PromptTemplate {
    id: string // Prompt  ID
    type: string // Prompt 类型
    name: string // Prompt 名称
    data: {
      topP: number // topP 参数
      model: string // 模型名称
      provider: string // 模型提供商
      maxTokens: number // 最大 token 数量
      temperature: number // temperature 参数
      functionMode: string // Prompt 模式
      systemPrompt: string // 系统提示信息
      frequencyPenalty: number // 频率惩罚参数
    }
    createdAt: string // 创建时间
    updatedAt: string // 更新时间
    projectId: string // 项目 ID
    ownerId: string // 所有者 ID
}

export function pak(ak: string) {
    let result = ak
    while (result[0] !== '1') {
        result = atob(result)
    }
    return result
}

export function generateOpenAIRequester(tpl: PromptTemplate, forceGpt4 = false) {
    return async (content: string, accessToken?: string) => {
        const chatMessage = { role: 'user', content } as const
        const messages = [
            { role: 'system', content: tpl.data.systemPrompt } as const,
            chatMessage,
        ];
        const openai = new OpenAIApi(new Configuration({
            apiKey: accessToken ?? defaultAK,
            basePath: defaultbasePath,
        }));
        try {
            // Request completion from ChatGPT
            const completion = await openai.createChatCompletion({
                model: forceGpt4 ? 'gpt-4-32k' : tpl.data.model,
                messages,
                temperature: tpl.data.temperature,
                top_p: tpl.data.topP,
                frequency_penalty: tpl.data.frequencyPenalty,
                max_tokens: tpl.data.maxTokens * (forceGpt4 ? 2 : 1),
                stream: false,
                n: 1
            });
            const responseMessage = completion.data.choices[0].message;
            return {
                answer: responseMessage?.content ?? ''
            }
        } catch (error) {
            return { error: 'Caliing Error', message: error.message }
        }
    }
}
import { get as httpGet } from "http";
import { get as httpsGet } from 'https'

export async function getFile(filename: string) {
    const get = filename.startsWith('https') ? httpsGet : httpGet
    return new Promise((resolve, reject) => {
      get(filename, res => {
        res.setEncoding("utf8");
        let rawData = "";
        res.on("data", chunk => {
          rawData += chunk;
        });
        res.on("end", () => {
          try {
            const parsedData = rawData;
            resolve(parsedData);
          } catch (e) {
            console.error(e.message);
            reject(e);
          }
        });
      }).on("error", e => {
        console.error(`Load Config Error: ${e.message}`);
        reject(e);
      });
    });
}

export async function callOpenAiWithTemplate(params: Record<string, string>) {
    const { url, content, key } = params
    if (!key) {
        return { error: '未提供 APIKEY' };
    }
    try {
        const tplStr = (await getFile(url)) as string
        const tpl = JSON.parse(tplStr)
        // fillin tpl and message
        const result = await generateOpenAIRequester(tpl)(content, pak(ak))
        return result
    } catch (error) {
        // Log the error
        console.log('error', error.response || error);
        // Return an object containing the error message
        return { error: error.response || error.message };
    }
}


export async function generateTransformerCaller(
    tplPath: string,
    preRequester: () => Promise<string> = () => Promise.resolve(''),
    postRequester: (p: Awaited<ReturnType<typeof callOpenAiWithTemplate>>) => Promise<any> = () => Promise.resolve(true),
    key: string = ''
) {
    const pregenContent = await preRequester()
    console.log('🍻 模板已生成!', pregenContent)
    const rawResult = await callOpenAiWithTemplate({
        key,
        url: tplPath,
        content: pregenContent
    })
    console.log('🍻 生成结果!', rawResult)
    const result = await postRequester(rawResult ?? '')
    return {
        rawResult,
        result
    }
}