import { callOmgMidjourneyCreate, callOmgMidjourneyRefine } from '@nibfe/adapi'
require('draftlog').into(console)
const colors = require('ansi-colors')
import fs from 'fs'
const cache = {}
const { Command } = require('commander')
const testProgram = new Command()

testProgram
  .option('-p, --prompt <string>', '生成 prompt')
  .option('-i, --id <string>', '根据指令 ID 优化')
  .option('-t, --task <string>', '任务 ID【优化必须】')

testProgram.parse(process.argv)

function printDraft(id: string, content: string[], indent: string) {
  let draft = cache[id]
  if (!draft) {
    cache[id] = draft = (console as any).draft('')
  }
  return draft(
    indent,
    ...content
  )
//   switch (type) {
//     case 'success':
//         return draft(
//             indent,
//             colors.green(`${figures.tick} ${item.name}: ${item.statusDescription}`),
//             colors.grey(`(耗时 ${item.runningTime}秒)`)
//         )
//   }
}

async function createOmgJourney(prompt: string) {
    const randomId = Math.random().toString(36).slice(3)
    const progresser = (progress: string) => {
        const percentage = parseInt(progress, 10)
        const barProgresser = Math.ceil(50 * (percentage / 100))
        const barRemainder = 50 - barProgresser
        const declaration = `${percentage}%`
        const barContents = [
            '[',
            ...Array.from({ length: barProgresser }).fill('='),
            ...Array.from({ length: barRemainder }).fill(' '),
            ']'
        ].join('')
        const contents = [
            colors.magenta('图片生成中...\n'),
            colors.yellow(`${declaration} ${barContents}`)
        ]
        return printDraft(randomId, contents, '')
    }

    const result = await callOmgMidjourneyCreate({
        prompt,
        generateType: 'NORMAL',
        progresser,
        key: '***************************************************',
    })
    
    if ('error' in result) {
        throw result.error
    }
    console.log(`生成完成！图片地址：${result.data.imageDcUrl}`)
    console.log()
    console.log(`>>> 【生成ID：${result.data.taskId}】进一步操作：`)
    console.log(result.data.actions.map((act: any) => `- 【操作：${act.label}】  ${act.action}`).join('\n'))
    fs.writeFileSync('mj-result.json', JSON.stringify(result.data), 'utf-8')
    console.log('🍻 生成结果已保存到文件中')
}


async function modifyOmgJourney(taskId: string, sampleId: string) {
    const randomId = Math.random().toString(36).slice(3)
    const progresser = (progress: string) => {
        const percentage = parseInt(progress, 10)
        const barProgresser = Math.ceil(50 * (percentage / 100))
        const barRemainder = 50 - barProgresser
        const declaration = `${percentage}%`
        const barContents = [
            '[',
            ...Array.from({ length: barProgresser }).fill('='),
            ...Array.from({ length: barRemainder }).fill(' '),
            ']'
        ].join('')
        const contents = [
            colors.magenta('图片生成中...\n'),
            colors.yellow(`${declaration} ${barContents}`)
        ]
        return printDraft(randomId, contents, '')
    }

    const result = await callOmgMidjourneyRefine({
        taskId: taskId,
        actionId: sampleId,
        generateType: 'NORMAL',
        progresser,
        key: '***************************************************',
    } as any)
    
    if ('error' in result) {
        throw result.error
    }
    console.log(`生成完成！图片地址：${result.data.imageDcUrl}`)
    console.log()
    console.log(`>>> 【生成ID：${result.data.taskId}】进一步操作：`)
    console.log(result.data.actions.map((act: any) => `- 【操作：${act.label}】  ${act.action}`).join('\n'))

    fs.writeFileSync('mj-result.json', JSON.stringify(result.data), 'utf-8')
    console.log('🍻 生成结果已保存到文件中')
}

// const opts = testProgram.parse()
if (testProgram.opts().id) {
    modifyOmgJourney(testProgram.opts().task, testProgram.opts().id)
} else {
    createOmgJourney(testProgram.opts().prompt)
}


// MJ::JOB::upsample::1::23197120-5803-4471-8546-6a475e7780f1'