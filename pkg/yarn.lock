# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@nibfe/adapi@^1.2.3":
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/@nibfe/adapi/download/@nibfe/adapi-1.2.3.tgz#e16a574911da5a434f21b068f4825e68817972d3"
  integrity sha1-4WpXSRHaWkNPIbBo9IJeaIF5ctM=
  dependencies:
    bufferutil "4.0.7"
    core-js "^3.8.0"
    crypto-js "4.1.1"
    openai "3.3.0"
    utf-8-validate "6.0.3"
    ws "8.13.0"

ansi-colors@^4.1.3:
  version "4.1.3"
  resolved "http://r.npm.sankuai.com/ansi-colors/download/ansi-colors-4.1.3.tgz#37611340eb2243e70cc604cad35d63270d48781b"
  integrity sha1-N2ETQOsiQ+cMxgTK011jJw1IeBs=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

axios@^0.26.0:
  version "0.26.1"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.26.1.tgz#1ede41c51fcf51bbbd6fd43669caaa4f0495aaa9"
  integrity sha1-Ht5BxR/PUbu9b9Q2acqqTwSVqqk=
  dependencies:
    follow-redirects "^1.14.8"

bufferutil@4.0.7:
  version "4.0.7"
  resolved "http://r.npm.sankuai.com/bufferutil/download/bufferutil-4.0.7.tgz#60c0d19ba2c992dd8273d3f73772ffc894c153ad"
  integrity sha1-YMDRm6LJkt2Cc9P3N3L/yJTBU60=
  dependencies:
    node-gyp-build "^4.3.0"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

core-js@^3.8.0:
  version "3.33.0"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-3.33.0.tgz#70366dbf737134761edb017990cf5ce6c6369c40"
  integrity sha1-cDZtv3NxNHYe2wF5kM9c5sY2nEA=

crypto-js@4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/crypto-js/download/crypto-js-4.1.1.tgz#9e485bcf03521041bd85844786b83fb7619736cf"
  integrity sha1-nkhbzwNSEEG9hYRHhrg/t2GXNs8=

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

draftlog@^1.0.13:
  version "1.0.13"
  resolved "http://r.npm.sankuai.com/draftlog/download/draftlog-1.0.13.tgz#2a415783955dbc6e2bb3b51a93c3d559f26b453d"
  integrity sha1-KkFXg5VdvG4rs7Uak8PVWfJrRT0=

follow-redirects@^1.14.8:
  version "1.15.2"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.2.tgz#b460864144ba63f2681096f274c4e57026da2c13"
  integrity sha1-tGCGQUS6Y/JoEJbydMTlcCbaLBM=

form-data@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  integrity sha1-k5Gdrq82HuUpWEubMWZNwSyfpFI=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-types@^2.1.12:
  version "2.1.35"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

node-gyp-build@^4.3.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/node-gyp-build/download/node-gyp-build-4.6.0.tgz#0c52e4cbf54bbd28b709820ef7b6a3c2d6209055"
  integrity sha1-DFLky/VLvSi3CYIO97ajwtYgkFU=

openai@3.3.0, openai@^3.2.1:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/openai/download/openai-3.3.0.tgz#a6408016ad0945738e1febf43f2fccca83a3f532"
  integrity sha1-pkCAFq0JRXOOH+v0Py/MyoOj9TI=
  dependencies:
    axios "^0.26.0"
    form-data "^4.0.0"

utf-8-validate@6.0.3:
  version "6.0.3"
  resolved "http://r.npm.sankuai.com/utf-8-validate/download/utf-8-validate-6.0.3.tgz#7d8c936d854e86b24d1d655f138ee27d2636d777"
  integrity sha1-fYyTbYVOhrJNHWVfE47ifSY213c=
  dependencies:
    node-gyp-build "^4.3.0"

ws@8.13.0:
  version "8.13.0"
  resolved "http://r.npm.sankuai.com/ws/download/ws-8.13.0.tgz#9a9fb92f93cf41512a0735c8f4dd09b8a1211cd0"
  integrity sha1-mp+5L5PPQVEqBzXI9N0JuKEhHNA=
