{"link": "https://habr.com/en/articles/746760/", "url": "https://habr.com/en/articles/746760/", "title": "Supercharge Your React Projects with Custom Hooks / Habr", "content": "HOW TO BECOME AN AUTHOR\nAll streams\nDevelopment\nAdmin\nDesign\nManagement\nMarketing\nPopSci\n<PERSON>chev\nJul 8 at 19:50\nSupercharge Your React Projects with Custom Hooks\nMedium\n42 min\n5.7K\nWebsite development\n*\nJavaScript\n*\nReactJS\n*\nTypeScript\n*\nVisual programming\n*\nTutorial\n\nIn this article, we dive into the world of custom React hooks and explore the incredible potential they hold for supercharging your work projects. With over 20 carefully crafted hooks at your disposal, I personally utilize these hooks in my own work projects, and now I'm excited to share them with you. From enhancing functionality to streamlining workflows, these custom hooks are designed to empower developers and deliver user-friendly experiences. Join us on this journey as we unleash the power of these 20+ hooks and unlock new levels of productivity and innovation in your React projects.\n\nGithub: https://github.com/sergeyleschev/react-custom-hooks\n\nReact Hooks are a feature introduced in React version 16.8 that revolutionized the way developers write and manage stateful logic in functional components. Previously, stateful logic could only be implemented in class components using lifecycle methods. However, with React Hooks, developers can now utilize state and other React features directly in functional components. Hooks provide a way to easily reuse stateful logic across multiple components, improving code reusability and reducing complexity. They enable developers to break down complex components into smaller, more manageable pieces, resulting in cleaner and more maintainable code. Hooks, such as useState and useEffect, allow developers to manage component state and handle side effects effortlessly. With their simplicity and flexibility, React Hooks have become an essential tool for building modern, efficient, and scalable React applications.\n\nReact custom hooks are reusable functions that allow developers to abstract and encapsulate complex logic in a reusable manner. Custom hooks are created by combining existing React hooks or other custom hooks. They enable developers to extract common logic from components and share it across different parts of an application. Custom hooks follow a naming convention of using the \"use\" prefix, which allows them to leverage the benefits of React's rules of hooks. By creating custom hooks, developers can modularize and organize their code, making it more readable, maintainable, and testable. These hooks can encapsulate any kind of logic, such as API calls, form handling, state management, or even abstracting external libraries. React custom hooks are a powerful tool that promotes code reusability and reduces duplication, making development more efficient and scalable.\n\nReact Custom Hooks @ 2023, S. Leschev. Google Engineering Level: L6+\n\nuseArray\n\nuseAsync\n\nuseClickOutside\n\nuseCookie\n\nuseCopyToClipboard\n\nuseDarkMode\n\nuseDebounce\n\nuseDebugInformation\n\nuseDeepCompareEffect\n\nuseEffectOnce\n\nuseEventListener\n\nuseFetch\n\nuseGeolocation\n\nuseHover\n\nuseLongPress\n\nuseMediaQuery\n\nuseOnlineStatus\n\nuseOnScreen\n\nusePrevious\n\nuseRenderCount\n\nuseScript\n\nuseStateWithHistory\n\nuseStateWithValidation\n\nuseStorage\n\nuseTimeout\n\nuseToggle\n\nuseTranslation\n\nuseUpdateEffect\n\nuseWindowSize\n\n1. useArray\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useState } from \"react\"\n\nexport default function useArray(defaultValue) {\n    const [array, setArray] = useState(defaultValue)\n\n    function push(element) {\n        setArray(a => [...a, element])\n    }\n\n    function filter(callback) {\n        setArray(a => a.filter(callback))\n    }\n\n    function update(index, newElement) {\n        setArray(a => [\n            ...a.slice(0, index),\n            newElement,\n            ...a.slice(index + 1, a.length),\n        ])\n    }\n\n    function remove(index) {\n        setArray(a => [...a.slice(0, index), ...a.slice(index + 1, a.length)])\n    }\n\n    function clear() {\n        setArray([])\n    }\n\n    return { array, set: setArray, push, filter, update, remove, clear }\n}\n\nThe useArray hook utilizes the useState hook from React to initialize and manage the array state. It returns an object with the following functions:\n\npush(element): Adds the specified element to the array.\n\nfilter(callback): Filters the array based on the provided callback function, removing elements that don't satisfy the condition.\n\nupdate(index, newElement): Replaces the element at the specified index with the newElement.\n\nremove(index): Removes the element at the specified index from the array.\n\nclear(): Clears the array, setting it to an empty array.\n\nThe advantages of using this custom hook are twofold: it simplifies the management of array states and provides a cleaner and more readable code structure. With the useArray hook, you can easily add, update, remove, filter, and clear elements in an array without dealing with complex logic.\n\nimport useArray from \"./useArray\"\n\nexport default function ArrayComponent() {\n    const { array, set, push, remove, filter, update, clear } = useArray([\n        1, 2, 3, 4, 5, 6,\n    ])\n\n    return (\n        <div>\n            <div>{array.join(\", \")}</div>\n            <button onClick={() => push(7)}>Add 7</button>\n            <button onClick={() => update(1, 9)}>Change Second Element To 9</button>\n            <button onClick={() => remove(1)}>Remove Second Element</button>\n            <button onClick={() => filter(n => n < 3)}>\n                Keep Numbers Less Than 4\n            </button>\n            <button onClick={() => set([1, 2])}>Set To 1, 2</button>\n            <button onClick={clear}>Clear</button>\n        </div>\n    )\n}\n2. useAsync\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useCallback, useEffect, useState } from \"react\"\n\nexport default function useAsync(callback, dependencies = []) {\n    const [loading, setLoading] = useState(true)\n    const [error, setError] = useState()\n    const [value, setValue] = useState()\n\n    const callbackMemoized = useCallback(() => {\n        setLoading(true)\n        setError(undefined)\n        setValue(undefined)\n        callback()\n            .then(setValue)\n            .catch(setError)\n            .finally(() => setLoading(false))\n    }, dependencies)\n\n    useEffect(() => {\n        callbackMemoized()\n    }, [callbackMemoized])\n\n    return { loading, error, value }\n}\n\nThe useAsync hook takes in a callback function that performs the asynchronous operation and an optional array of dependencies. It returns an object with three properties: loading, error, and value. The loading property indicates whether the operation is currently in progress, while the error property holds any error messages encountered during the process. Finally, the value property contains the resolved value of the asynchronous operation.\n\nOne of the significant advantages of useAsync is its ability to memoize the callback function using useCallback. This ensures that the callback is only recreated when the dependencies change, preventing unnecessary re-renders and optimizing performance. Additionally, the hook employs the useState and useEffect hooks to manage the loading state and invoke the memoized callback function when necessary.\n\nUseAsync can be employed in a wide range of scenarios. Whether you're fetching data from an API, performing computations, or handling form submissions, this custom hook simplifies the management of asynchronous operations throughout your React components. Its flexibility and ease of use make it a valuable addition to any React project.\n\nBy utilizing useAsync, you can streamline your codebase, enhance reusability, and maintain a consistent and reliable user experience. Give it a try in your next React project and witness the power of simplified asynchronous operations.\n\nimport useAsync from \"./useAsync\"\n\nexport default function AsyncComponent() {\n    const { loading, error, value } = useAsync(() => {\n        return new Promise((resolve, reject) => {\n            const success = false\n            setTimeout(() => {\n                success ? resolve(\"Hi\") : reject(\"Error\")\n            }, 1000)\n        })\n    })\n\n    return (\n        <div>\n            <div>Loading: {loading.toString()}</div>\n            <div>{error}</div>\n            <div>{value}</div>\n        </div>\n    )\n}\n3. useClickOutside\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport useEventListener from \"../useEventListener/useEventListener\"\n\nexport default function useClickOutside(ref, cb) {\n    useEventListener(\"click\", e => {\n        if (ref.current == null || ref.current.contains(e.target)) return\n        cb(e)\n    }, document)\n}\n\nThe useClickOutside hook is designed to simplify the process of detecting clicks outside a specified component. By utilizing the useEventListener hook, it listens for click events on the document level, allowing you to trigger a callback function when a click occurs outside the provided component's reference.\n\nOne of the main advantages of useClickOutside is its ease of use. Simply import the hook into your component and pass the desired component's reference and a callback function. The hook takes care of the event listener setup and cleanup, saving you time and effort. Plus, it works seamlessly with functional components using the useState and useRef hooks.\n\nThe potential applications for useClickOutside are endless. It is particularly useful when implementing modal windows, dropdown menus, or any element that should be closed when a user interacts with anything outside of it. By incorporating useClickOutside, you can enhance the user experience by providing intuitive and efficient interactions.\n\nimport { useRef, useState } from \"react\"\nimport useClickOutside from \"./useClickOutside\"\n\nexport default function ClickOutsideComponent() {\n    const [open, setOpen] = useState(false)\n    const modalRef = useRef()\n\n    useClickOutside(modalRef, () => {\n        if (open) setOpen(false)\n    })\n\n    return (\n        <>\n            <button onClick={() => setOpen(true)}>Open</button>\n            <div\n                ref={modalRef}\n                style={{\n                    display: open ? \"block\" : \"none\",\n                    backgroundColor: \"blue\",\n                    color: \"white\",\n                    width: \"100px\",\n                    height: \"100px\",\n                    position: \"absolute\",\n                    top: \"calc(50% - 50px)\",\n                    left: \"calc(50% - 50px)\",\n                }}\n            >\n                <span>Modal</span>\n            </div>\n        </>\n    )\n}\n\nTo see useClickOutside in action, take a look at the example above. In this case, the ClickOutsideComponent utilizes the hook to toggle the visibility of a modal window. When the user clicks outside the modal, the provided callback function sets the open state to false, closing the modal. This way, the component offers a sleek and user-friendly way to manage the modal's visibility.\n\n4. useCookie\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useState, useCallback } from \"react\"\nimport Cookies from \"js-cookie\"\n\nexport default function useCookie(name, defaultValue) {\n    const [value, setValue] = useState(() => {\n        const cookie = Cookies.get(name)\n        if (cookie) return cookie\n        Cookies.set(name, defaultValue)\n        return defaultValue\n    })\n\n    const updateCookie = useCallback(\n        (newValue, options) => {\n            Cookies.set(name, newValue, options)\n            setValue(newValue)\n        },\n        [name]\n    )\n\n    const deleteCookie = useCallback(() => {\n        Cookies.remove(name)\n        setValue(null)\n    }, [name])\n\n    return [value, updateCookie, deleteCookie]\n}\n\nThe useCookie hook allows you to effortlessly handle cookies by providing a concise interface. Upon initialization, useCookie retrieves the cookie value with the specified name. If the cookie exists, it returns its value; otherwise, it sets the cookie to the default value provided. This ensures a seamless experience for your users, as the desired data is readily available.\n\nOne of the key advantages of this custom hook is the ability to update the cookie value. The updateCookie function, returned by useCookie, enables you to modify the value of the cookie. By invoking this function with a new value and optional options, such as expiration or path, you can instantly update the cookie. Additionally, the hook conveniently updates the state, keeping your application in sync with the modified cookie.\n\nIn scenarios where you need to remove a cookie, the deleteCookie function comes to the rescue. Simply call this function, and it will remove the specified cookie from the browser. The hook takes care of updating the state, ensuring that your application reflects the removal of the cookie.\n\nThe useCookie custom hook is highly versatile and can be utilized in various contexts. It is particularly beneficial when working with user preferences, authentication tokens, or any data that needs to persist across different sessions. Whether you are building a simple login form, a shopping cart, or a feature-rich application, useCookie simplifies cookie management, saving you valuable development time.\n\nimport useCookie from \"./useCookie\"\n\nexport default function CookieComponent() {\n    const [value, update, remove] = useCookie(\"name\", \"John\")\n\n    return (\n        <>\n            <div>{value}</div>\n            <button onClick={() => update(\"Sally\")}>Change Name To Sally</button>\n            <button onClick={remove}>Delete Name</button>\n        </>\n    )\n}\n5. useCopyToClipboard\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useState } from \"react\"\nimport copy from \"copy-to-clipboard\"\n\nexport default function useCopyToClipboard() {\n    const [value, setValue] = useState()\n    const [success, setSuccess] = useState()\n\n    const copyToClipboard = (text, options) => {\n        const result = copy(text, options)\n        if (result) setValue(text)\n        setSuccess(result)\n    }\n\n    return [copyToClipboard, { value, success }]\n}\n\nCopying text to the clipboard in a React application can be a tedious task. To simplify this process, I've created a powerful custom hook called useCopyToClipboard. With just a few lines of code, this hook streamlines the copy-to-clipboard functionality, providing developers with a hassle-free solution.\n\nThe useCopyToClipboard hook utilizes the useState hook from React, along with the copy-to-clipboard library, to achieve its functionality. By invoking this custom hook, you gain access to two essential features: copyToClipboard and its accompanying state variables.\n\nThe copyToClipboard function takes in two parameters: the text to be copied and optional configuration options. It handles the copying process and updates the state accordingly. When successful, the provided text is set as the current value, and the success state is set to true. Conversely, if the copying fails, the success state remains false.\n\nTo demonstrate the power of useCopyToClipboard, let's consider a practical implementation. Suppose you have a component called CopyToClipboardComponent. By utilizing this custom hook, you can effortlessly copy text by invoking the copyToClipboard function, which accepts the desired text as an argument. The success state variable provides immediate feedback, allowing you to display appropriate messages or UI elements based on the copying outcome.\n\nThe useCopyToClipboard hook is incredibly versatile and can be employed in various scenarios. It is particularly useful in situations where copying text, such as URLs, shareable content, or user-generated data, is required. Whether you're building a blogging platform, a social media application, or any other React-based project, useCopyToClipboard simplifies the process of copying text, enhancing user experience and productivity.\n\nimport useCopyToClipboard from \"./useCopyToClipboard\"\n\nexport default function CopyToClipboardComponent() {\n    const [copyToClipboard, { success }] = useCopyToClipboard()\n\n    return (\n        <>\n            <button onClick={() => copyToClipboard(\"This was copied\")}>\n                {success ? \"Copied\" : \"Copy Text\"}\n            </button>\n            <input type=\"text\" />\n        </>\n    )\n}\n6. useDarkMode\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useEffect } from \"react\"\nimport useMediaQuery from \"../useMediaQuery/useMediaQuery\"\nimport { useLocalStorage } from \"../useStorage/useStorage\"\n\nexport default function useDarkMode() {\n    const [darkMode, setDarkMode] = useLocalStorage(\"useDarkMode\")\n    const prefersDarkMode = useMediaQuery(\"(prefers-color-scheme: dark)\")\n    const enabled = darkMode ?? prefersDarkMode\n\n    useEffect(() => {\n        document.body.classList.toggle(\"dark-mode\", enabled)\n    }, [enabled])\n\n    return [enabled, setDarkMode]\n}\n\nThis custom hook combines two other handy hooks useMediaQuery and useStorage, to provide a seamless dark mode experience. It automatically detects the user's preferred color scheme and persists the dark mode state in the browser's local storage.\n\nOne of the main advantages of \"useDarkMode\" is its simplicity. With just a few lines of code, you can enable dark mode in your React application. By invoking this hook, you'll receive the current dark mode state and a function to toggle it.\n\nThe \"useDarkMode\" hook dynamically updates the HTML body class to apply the \"dark-mode\" styling whenever dark mode is enabled. This approach ensures consistency across all components without the need for manual class manipulation.\n\nbody.dark-mode {\n    background-color: #333;\n}\n\nYou can use the \"useDarkMode\" hook in various scenarios. Whether you're building a blog, e-commerce platform, or a content-heavy application, dark mode can enhance the user experience, reduce eye strain, and conserve device battery life. The possibilities are endless, and this custom hook makes it a breeze to implement.\n\nTo make it even easier, I've included a simple example component, \"DarkModeComponent,\" that showcases how to use the \"useDarkMode\" hook. By clicking the \"Toggle Dark Mode\" button, you can instantly switch between light and dark themes. The button's appearance changes dynamically, reflecting the current mode.\n\nimport useDarkMode from \"./useDarkMode\"\nimport \"./body.css\"\n\nexport default function DarkModeComponent() {\n    const [darkMode, setDarkMode] = useDarkMode()\n    return (\n        <button\n            onClick={() => setDarkMode(prevDarkMode => !prevDarkMode)}\n            style={{\n                border: `1px solid ${darkMode ? \"white\" : \"black\"}`,\n                background: \"none\",\n                color: darkMode ? \"white\" : \"black\",\n            }}\n        >\n            Toggle Dark Mode\n        </button>\n    )\n}\n7. useDebounce\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useEffect } from \"react\"\nimport useTimeout from \"../useTimeout/useTimeout\"\n\nexport default function useDebounce(callback, delay, dependencies) {\n    const { reset, clear } = useTimeout(callback, delay)\n    useEffect(reset, [...dependencies, reset])\n    useEffect(clear, [])\n}\n\nThe useDebounce hook leverages the useTimeout hook internally to delay the execution of a callback function until a specified delay has passed. By doing so, it prevents frequent updates caused by rapid input changes or repeated events, allowing for smoother interactions and reduced resource consumption.\n\nOne of the main advantages of useDebounce is its simplicity and flexibility. By wrapping your callback function, delay duration, and any dependencies in this custom hook, you can effortlessly implement debouncing functionality without cluttering your component code. The hook takes care of managing the timeout and clears it when necessary, ensuring that the callback is only triggered after the specified delay and with the latest dependencies.\n\nWhere can you use useDebounce? The possibilities are endless! This custom hook is particularly beneficial in scenarios where you need to handle user input, such as search bars or form fields, where you want to delay the execution of an action until the user has finished typing or interacting. It's also useful for optimizing network requests, ensuring that requests are sent only after the user has stopped typing or selecting options.\n\nimport { useState } from \"react\"\nimport useDebounce from \"./useDebounce\"\n\nexport default function DebounceComponent() {\n    const [count, setCount] = useState(10)\n    useDebounce(() => alert(count), 1000, [count])\n    return (\n        <div>\n            <div>{count}</div>\n            <button onClick={() => setCount(c => c + 1)}>Increment</button>\n        </div>\n    )\n}\n\nIn the example above, we showcase the power of useDebounce by implementing a simple counter component called DebounceComponent. Each time the user clicks the \"Increment\" button, the count state updates. However, instead of immediately alerting the count value, we debounce the alert function using useDebounce. The count value will only be alerted after a 1-second delay, effectively preventing excessive alerts when the button is clicked rapidly.\n\n8. useDebugInformation\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useEffect, useRef } from \"react\"\nimport useRenderCount from \"../useRenderCount/useRenderCount\"\n\nexport default function useDebugInformation(componentName, props) {\n    const count = useRenderCount()\n    const changedProps = useRef({})\n    const previousProps = useRef(props)\n    const lastRenderTimestamp = useRef(Date.now())\n    const propKeys = Object.keys({ ...props, ...previousProps })\n    changedProps.current = propKeys.reduce((obj, key) => {\n        if (props[key] === previousProps.current[key]) return obj\n        return {\n            ...obj,\n            [key]: { previous: previousProps.current[key], current: props[key] },\n        }\n    }, {})\n    const info = {\n        count,\n        changedProps: changedProps.current,\n        timeSinceLastRender: Date.now() - lastRenderTimestamp.current,\n        lastRenderTimestamp: lastRenderTimestamp.current,\n    }\n    useEffect(() => {\n        previousProps.current = props\n        lastRenderTimestamp.current = Date.now()\n        console.log(\"[debug-info]\", componentName, info)\n    })\n    return info\n}\n\nWhen it comes to debugging React components, having access to detailed information about renders and prop changes can be incredibly useful. That's where the useDebugInformation custom hook comes in. Created by [Your Name], this advanced hook provides developers with valuable insights into their components' behavior and helps identify performance bottlenecks or unexpected rendering patterns.\n\nOne of the main advantages of useDebugInformation is its simplicity. By integrating just a few lines of code into your component, you gain access to a wealth of debugging data. The hook tracks the number of renders, changed props, time since the last render, and the timestamp of the last render. This comprehensive information empowers you to analyze component behavior more effectively and make informed decisions when optimizing your application.\n\nThe useDebugInformation hook can be applied in various scenarios. For instance, imagine you're working on a complex form component where certain props trigger updates or affect rendering. By utilizing useDebugInformation, you can easily monitor how these props impact your component's performance and whether unnecessary re-renders are occurring. Additionally, the hook can be invaluable when investigating why a specific component is not updating as expected or when fine-tuning optimizations in a performance-critical application.\n\nTo implement useDebugInformation, simply import it into your React component, along with any other necessary hooks. In the example provided, the DebugInformationComponent utilizes the useDebugInformation hook within the ChildComponent. By passing the component name and props to the hook, you gain access to an info object containing all the relevant debugging data. This object can then be displayed or logged for further analysis.\n\nimport useDebugInformation from \"./useDebugInformation\"\nimport useToggle from \"../useToggle/useToggle\"\nimport { useState } from \"react\"\n\nexport default function DebugInformationComponent() {\n    const [boolean, toggle] = useToggle(false)\n    const [count, setCount] = useState(0)\n    return (\n        <>\n            <ChildComponent boolean={boolean} count={count} />\n            <button onClick={toggle}>Toggle</button>\n            <button onClick={() => setCount(prevCount => prevCount + 1)}>\n                Increment\n            </button>\n        </>\n    )\n}\nfunction ChildComponent(props) {\n    const info = useDebugInformation(\"ChildComponent\", props)\n    return (\n        <>\n            <div>{props.boolean.toString()}</div>\n            <div>{props.count}</div>\n            <div>{JSON.stringify(info, null, 2)}</div>\n        </>\n    )\n}\n9. useDeepCompareEffect\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useEffect, useRef } from \"react\"\nimport isEqual from \"lodash/fp/isEqual\"\n\nexport default function useDeepCompareEffect(callback, dependencies) {\n    const currentDependenciesRef = useRef()\n    if (!isEqual(currentDependenciesRef.current, dependencies)) {\n        currentDependenciesRef.current = dependencies\n    }\n    useEffect(callback, [currentDependenciesRef.current])\n}\n\nManaging dependencies in React can be a challenge, especially when dealing with complex data structures or nested objects. That's where the useDeepCompareEffect custom hook comes in handy. Created to tackle the limitations of the default useEffect hook, useDeepCompareEffect ensures that the effect callback is only triggered when the dependencies have deeply changed, using lodash's isEqual function for accurate comparison.\n\nOne of the key advantages of useDeepCompareEffect is its ability to prevent unnecessary re-renders. By performing a deep comparison between the current and previous dependencies, the hook intelligently determines if the effect should be triggered, leading to optimized performance in scenarios where shallow comparisons fall short.\n\nThis custom hook can be especially useful when dealing with complex state objects, such as when you have deeply nested data structures or multiple interconnected states that need to be tracked. It enables you to define dependencies that accurately reflect the specific changes you want to track, ensuring that the effect is executed only when it is absolutely necessary.\n\nYou can easily incorporate useDeepCompareEffect into your React components by importing it and utilizing it in place of the traditional useEffect hook. By passing the effect callback and an array of dependencies, you can ensure that your effect runs efficiently and effectively.\n\nimport { useEffect, useState, useRef } from \"react\"\nimport useDeepCompareEffect from \"./useDeepCompareEffect\"\n\nexport default function DeepCompareEffectComponent() {\n    const [age, setAge] = useState(0)\n    const [otherCount, setOtherCount] = useState(0)\n    const useEffectCountRef = useRef()\n    const useDeepCompareEffectCountRef = useRef()\n    const person = { age: age, name: \"Sergey\" }\n    useEffect(() => {\n        useEffectCountRef.current.textContent =\n            parseInt(useEffectCountRef.current.textContent) + 1\n    }, [person])\n    useDeepCompareEffect(() => {\n        useDeepCompareEffectCountRef.current.textContent =\n            parseInt(useDeepCompareEffectCountRef.current.textContent) + 1\n    }, [person])\n    return (\n        <div>\n            <div>\n                useEffect: <span ref={useEffectCountRef}>0</span>\n            </div>\n            <div>\n                useDeepCompareEffect: <span ref={useDeepCompareEffectCountRef}>0</span>\n            </div>\n            <div>Other Count: {otherCount}</div>\n            <div>{JSON.stringify(person)}</div>\n            <button onClick={() => setAge(currentAge => currentAge + 1)}>\n                Increment Age\n            </button>\n            <button onClick={() => setOtherCount(count => count + 1)}>\n                Increment Other Count\n            </button>\n        </div>\n    )\n}\n10. useEffectOnce\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useEffect } from \"react\"\n\nexport default function useEffectOnce(cb) {\n    useEffect(cb, [])\n}\n\nThe useEffectOnce hook is designed to streamline the process of running effects only once when a component mounts. With just a few lines of code, you can eliminate the need to manually specify an empty dependency array ([]). Here's how it works:\n\nBy encapsulating the repetitive useEffect pattern, useEffectOnce allows you to focus on the logic within the effect function itself. This elegant solution saves you from writing boilerplate code repeatedly and helps keep your component files clean and concise.\n\nTo showcase the power of useEffectOnce, let's consider a practical example:\n\nimport { useState } from \"react\"\nimport useEffectOnce from \"./useEffectOnce\"\n\nexport default function EffectOnceComponent() {\n    const [count, setCount] = useState(0)\n    useEffectOnce(() => alert(\"Hi\"))\n    return (\n        <>\n            <div>{count}</div>\n            <button onClick={() => setCount(c => c + 1)}>Increment</button>\n        </>\n    )\n}\n\nIn this case, when EffectOnceComponent mounts, the useEffectOnce hook triggers the alert \"Hi\" exactly once. It frees you from manually managing the effect dependencies and ensures your effect runs efficiently.\n\nThis custom hook is incredibly versatile and can be utilized in various scenarios. Whether you need to fetch initial data, set up event listeners, or initialize third-party libraries, useEffectOnce simplifies the process and promotes cleaner code organization.\n\n11. useEventListener\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useEffect, useRef } from \"react\"\n\nexport default function useEventListener(\n    eventType,\n    callback,\n    element = window\n) {\n    const callbackRef = useRef(callback)\n    useEffect(() => {\n        callbackRef.current = callback\n    }, [callback])\n    useEffect(() => {\n        if (element == null) return\n        const handler = e => callbackRef.current(e)\n        element.addEventListener(eventType, handler)\n        return () => element.removeEventListener(eventType, handler)\n    }, [eventType, element])\n}\n\nOne of the major advantages of useEventListener is its flexibility. You can specify the event type, callback function, and even the element where the event listener should be attached. This flexibility allows you to tailor event handling to your specific needs, enhancing the reusability of your code.\n\nThe hook also takes advantage of the useRef hook to maintain a stable reference to the callback function. This ensures that the most up-to-date version of the callback is used, even if it changes during the component's lifecycle. This dynamic behavior enables you to handle events with precision and respond to changes in your application's state.\n\nThe useEventListener hook is a versatile tool that can be used in a wide range of scenarios. Whether you need to capture keyboard events, listen for scroll events, or interact with user input, this hook has got you covered. Its simplicity and elegance make it an ideal choice for any React project, from small-scale applications to large-scale enterprise solutions.\n\nTo demonstrate the power of useEventListener, consider the EventListenerComponent provided. It utilizes the hook to track the last key pressed by the user. With just a few lines of code, you can effortlessly handle keydown events and update the component's state accordingly. This example highlights the ease and effectiveness of useEventListener, showcasing its ability to simplify event-driven interactions in React applications.\n\nimport { useState } from \"react\"\nimport useEventListener from \"./useEventListener\"\n\nexport default function EventListenerComponent() {\n    const [key, setKey] = useState(\"\")\n    useEventListener(\"keydown\", e => {\n        setKey(e.key)\n    })\n    return <div>Last Key: {key}</div>\n}\n12. useFetch\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport useAsync from \"../useAsync/useAsync\"\n\nconst DEFAULT_OPTIONS = {\n    headers: { \"Content-Type\": \"application/json\" },\n}\nexport default function useFetch(url, options = {}, dependencies = []) {\n    return useAsync(() => {\n        return fetch(url, { ...DEFAULT_OPTIONS, ...options }).then(res => {\n            if (res.ok) return res.json()\n            return res.json().then(json => Promise.reject(json))\n        })\n    }, dependencies)\n}\n\nOne of the key advantages of useFetch is its simplicity. By abstracting away the fetch logic into a reusable hook, developers can quickly and effortlessly make HTTP requests and handle responses without repetitive boilerplate code. With just a few lines, useFetch handles the network request, parses the JSON response, and provides the resulting data.\n\nThe useFetch hook also offers flexibility through its customizable options parameter. Developers can pass additional headers, query parameters, or request options as needed, ensuring compatibility with various APIs. The hook follows best practices by providing default options for setting the Content-Type header as application/json, promoting clean and consistent code.\n\nAnother noteworthy feature of useFetch is its support for dependency tracking. By specifying an array of dependencies, developers can control when the hook triggers a new request. This feature enhances performance optimization, allowing for selective data updates based on changes in the dependency array.\n\nThis versatile hook can be utilized in numerous scenarios. For example, in a React component that needs to fetch and display dynamic data, useFetch simplifies the process. It takes care of handling loading and error states, keeping the component clean and focused on rendering the received data. Additionally, useFetch is particularly useful in scenarios where the fetched data is based on dynamic variables or user interactions, as demonstrated in the FetchComponent example.\n\nimport { useState } from \"react\"\nimport useFetch from \"./useFetch\"\n\nexport default function FetchComponent() {\n    const [id, setId] = useState(1)\n    const { loading, error, value } = useFetch(\n        `https://jsonplaceholder.typicode.com/todos/${id}`,\n        {},\n        [id]\n    )\n    return (\n        <div>\n            <div>{id}</div>\n            <button onClick={() => setId(currentId => currentId + 1)}>\n                Increment ID\n            </button>\n            <div>Loading: {loading.toString()}</div>\n            <div>{JSON.stringify(error, null, 2)}</div>\n            <div>{JSON.stringify(value, null, 2)}</div>\n        </div>\n    )\n}\n13. useGeolocation\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useState, useEffect } from \"react\"\n\nexport default function useGeolocation(options) {\n    const [loading, setLoading] = useState(true)\n    const [error, setError] = useState()\n    const [data, setData] = useState({})\n    useEffect(() => {\n        const successHandler = e => {\n            setLoading(false)\n            setError(null)\n            setData(e.coords)\n        }\n        const errorHandler = e => {\n            setError(e)\n            setLoading(false)\n        }\n        navigator.geolocation.getCurrentPosition(\n            successHandler,\n            errorHandler,\n            options\n        )\n        const id = navigator.geolocation.watchPosition(\n            successHandler,\n            errorHandler,\n            options\n        )\n        return () => navigator.geolocation.clearWatch(id)\n    }, [options])\n    return { loading, error, data }\n}\n\nThe useGeolocation hook utilizes React's useState and useEffect hooks to manage the state of loading, errors, and geolocation data. It takes an optional \"options\" parameter to customize the geolocation behavior, allowing you to fine-tune the accuracy and other settings based on your specific needs.\n\nOne of the key advantages of useGeolocation is its simplicity. By encapsulating the complex logic required for geolocation access and handling, this hook provides a clean and reusable solution. The hook automatically handles the loading state, updating it when geolocation data is being fetched, and sets the error state if any issues arise during the process.\n\nThe useGeolocation hook also incorporates the watchPosition method from the Geolocation API, which enables continuous monitoring of the user's position. This can be useful in scenarios where real-time updates of the user's location are required, such as in tracking applications or interactive maps.\n\nTo use this hook, simply import useGeolocation into your component and destructure the loading, error, and data variables. The data object contains the latitude and longitude values, allowing you to display the user's location on your UI effortlessly. The loading variable informs you of the current state of geolocation retrieval, and the error variable provides any error messages, if applicable.\n\nimport useGeolocation from \"./useGeolocation\"\n\nexport default function GeolocationComponent() {\n    const {\n        loading,\n        error,\n        data: { latitude, longitude },\n    } = useGeolocation()\n    return (\n        <>\n            <div>Loading: {loading.toString()}</div>\n            <div>Error: {error?.message}</div>\n            <div>\n                {latitude} x {longitude}\n            </div>\n        </>\n    )\n}\n\nThe GeolocationComponent showcased above demonstrates a basic implementation of useGeolocation. It renders the loading state, error message (if any), and the user's latitude and longitude values. With just a few lines of code, you can seamlessly integrate geolocation functionality into your React applications.\n\n14. useHover\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useState } from \"react\"\nimport useEventListener from \"../useEventListener/useEventListener\"\n\nexport default function useHover(ref) {\n    const [hovered, setHovered] = useState(false)\n    useEventListener(\"mouseover\", () => setHovered(true), ref.current)\n    useEventListener(\"mouseout\", () => setHovered(false), ref.current)\n    return hovered\n}\n\nThis lightweight hook leverages the useState and useEventListener hooks from React to keep track of the hover state. By simply passing a ref to the useHover hook, you can start receiving accurate hover events. The hook listens for \"mouseover\" and \"mouseout\" events, updating the hovered state accordingly.\n\nOne of the key advantages of useHover is its simplicity and reusability. By encapsulating the hover logic within the hook, you can easily use it across multiple components without duplicating code. This promotes clean and maintainable code, saving you time and effort in the long run.\n\nUseHover can be used in a variety of scenarios. Whether you need to highlight an element on hover, trigger additional actions, or dynamically change styles, this custom hook has got you covered. It provides a seamless way to enhance the interactivity and user experience of your React components.\n\nimport { useRef } from \"react\"\nimport useHover from \"./useHover\"\n\nexport default function HoverComponent() {\n    const elementRef = useRef()\n    const hovered = useHover(elementRef)\n    return (\n        <div\n            ref={elementRef}\n            style={{\n                backgroundColor: hovered ? \"blue\" : \"red\",\n                width: \"100px\",\n                height: \"100px\",\n                position: \"absolute\",\n                top: \"calc(50% - 50px)\",\n                left: \"calc(50% - 50px)\",\n            }}\n        />\n    )\n}\n\nTo demonstrate its power, consider the HoverComponent example above. By applying the useHover hook to the elementRef, the background color of the div dynamically changes between blue and red depending on the hover state. This simple yet effective implementation showcases the potential of useHover in creating interactive and engaging UI components.\n\n15. useLongPress\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport useEventListener from \"../useEventListener/useEventListener\"\nimport useTimeout from \"../useTimeout/useTimeout\"\nimport useEffectOnce from \"../useEffectOnce/useEffectOnce\"\n\nexport default function useLongPress(ref, cb, { delay = 250 } = {}) {\n    const { reset, clear } = useTimeout(cb, delay)\n    useEffectOnce(clear)\n    useEventListener(\"mousedown\", reset, ref.current)\n    useEventListener(\"touchstart\", reset, ref.current)\n    useEventListener(\"mouseup\", clear, ref.current)\n    useEventListener(\"mouseleave\", clear, ref.current)\n    useEventListener(\"touchend\", clear, ref.current)\n}\n\nOne of the key advantages of useLongPress is its simplicity. By utilizing this hook, developers can easily define a long-press action on any element in their React application. With just a few lines of code, the hook takes care of handling the intricacies of tracking the long-press duration and triggering the associated callback function.\n\nThe useLongPress hook offers flexibility through customizable options. Developers can specify the desired delay for a long press, allowing them to fine-tune the duration required for an action to be triggered. Additionally, the hook intelligently integrates with other custom hooks like useTimeout, useEventListener, and useEffectOnce, enhancing code reusability and maintainability.\n\nThe applications for useLongPress are wide-ranging. Whether you're developing a touch-sensitive UI, implementing context menus, or creating custom gestures, this hook proves to be a valuable tool. From mobile applications to complex web interfaces, useLongPress provides an elegant solution for incorporating long-press interactions that elevate user engagement and improve overall usability.\n\nimport { useRef } from \"react\"\nimport useLongPress from \"./useLongPress\"\n\nexport default function LongPressComponent() {\n    const elementRef = useRef()\n    useLongPress(elementRef, () => alert(\"Long Press\"))\n    return (\n        <div\n            ref={elementRef}\n            style={{\n                backgroundColor: \"red\",\n                width: \"100px\",\n                height: \"100px\",\n                position: \"absolute\",\n                top: \"calc(50% - 50px)\",\n                left: \"calc(50% - 50px)\",\n            }}\n        />\n    )\n}\n16. useMediaQuery\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useState, useEffect } from \"react\"\nimport useEventListener from \"../useEventListener/useEventListener\"\n\nexport default function useMediaQuery(mediaQuery) {\n    const [isMatch, setIsMatch] = useState(false)\n    const [mediaQueryList, setMediaQueryList] = useState(null)\n    useEffect(() => {\n        const list = window.matchMedia(mediaQuery)\n        setMediaQueryList(list)\n        setIsMatch(list.matches)\n    }, [mediaQuery])\n    useEventListener(\"change\", e => setIsMatch(e.matches), mediaQueryList)\n    return isMatch\n}\n\nThe useMediaQuery hook allows you to dynamically update your UI based on a given media query. Simply pass in the desired media query as a parameter, and the hook will return a boolean value indicating whether the media query matches the current viewport size.\n\nOne of the key advantages of this custom hook is its simplicity and reusability. With just a few lines of code, you can effortlessly implement responsive behavior throughout your application. Whether you need to conditionally render components, apply specific styles, or trigger different functionality based on screen size, useMediaQuery has got you covered.\n\nThis hook is not limited to specific use cases; it can be utilized in a variety of scenarios. For instance, you can use it to dynamically adjust the layout of a navigation menu, hide or show certain elements based on screen size, or even optimize the loading of data based on the available space. The possibilities are endless, and the useMediaQuery hook empowers you to deliver a seamless user experience across different devices and screen sizes.\n\nimport useMediaQuery from \"./useMediaQuery\"\n\nexport default function MediaQueryComponent() {\n    const isLarge = useMediaQuery(\"(min-width: 200px)\")\n    return <div>Large: {isLarge.toString()}</div>\n}\n17. useOnlineStatus\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useState } from \"react\"\nimport useEventListener from \"../useEventListener/useEventListener\"\n\nexport default function useOnlineStatus() {\n    const [online, setOnline] = useState(navigator.onLine)\n    useEventListener(\"online\", () => setOnline(navigator.onLine))\n    useEventListener(\"offline\", () => setOnline(navigator.onLine))\n    return online\n}\n\nOne of the main advantages of \"useOnlineStatus\" is its simplicity. By importing and using this hook in your component, you can effortlessly access the online status of the user. The hook internally uses the \"navigator.onLine\" property to determine the initial online status and dynamically updates it whenever the user's connectivity changes.\n\nTo use this hook, all you need to do is call it within your functional component, just like the \"OnlineStatusComponent\" example demonstrates. It returns a boolean value indicating whether the user is currently online or offline. You can then utilize this information to provide real-time feedback to your users or make decisions based on their online status.\n\nThe \"useOnlineStatus\" hook can find applications in a wide range of scenarios. For instance, you can enhance user experience by displaying a visual indicator when the user loses their internet connection, allowing them to take appropriate actions. Additionally, you can conditionally render certain components or trigger specific behaviors based on the user's online status. The possibilities are endless, and this hook opens up new opportunities for building robust and responsive React applications.\n\nimport useOnlineStatus from \"./useOnlineStatus\"\n\nexport default function OnlineStatusComponent() {\n    const online = useOnlineStatus()\n    return <div>{online.toString()}</div>\n}\n18. useOnScreen\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useEffect, useState } from \"react\"\n\nexport default function useOnScreen(ref, rootMargin = \"0px\") {\n    const [isVisible, setIsVisible] = useState(false)\n    useEffect(() => {\n        if (ref.current == null) return\n        const observer = new IntersectionObserver(\n            ([entry]) => setIsVisible(entry.isIntersecting),\n            { rootMargin }\n        )\n        observer.observe(ref.current)\n        return () => {\n            if (ref.current == null) return\n            observer.unobserve(ref.current)\n        }\n    }, [ref.current, rootMargin])\n    return isVisible\n}\n\nThe useOnScreen hook leverages the power of the Intersection Observer API, making it efficient and reliable. By simply providing a ref to the element you want to monitor, useOnScreen will notify you when it enters or exits the viewport.\n\nOne of the key advantages of useOnScreen is its simplicity. With just a few lines of code, you can detect if an element is visible and respond accordingly. This can be immensely useful in scenarios where you want to trigger animations, lazy load images, or load additional content as the user scrolls.\n\nTo use this hook, first import it into your component file. Then, create a ref using the useRef hook to target the desired element. Pass the ref as the first argument to the useOnScreen hook, and you're all set! You can also provide an optional rootMargin value to adjust the visible threshold.\n\nIn our example code, the OnScreenComponentComponent demonstrates how to use the useOnScreen hook. By attaching the ref to the second header element, we can display a \"(Visible)\" text when it enters the viewport. Feel free to customize the logic within your component to suit your specific needs.\n\nimport { useRef } from \"react\"\nimport useOnScreen from \"./useOnScreen\"\n\nexport default function OnScreenComponentComponent() {\n    const headerTwoRef = useRef()\n    const visible = useOnScreen(headerTwoRef, \"-100px\")\n    return (\n        <div>\n            <h1>Header</h1>\n            <div>\n              ...\n            </div>\n            <h1 ref={headerTwoRef}>Header 2 {visible && \"(Visible)\"}</h1>\n            <div>\n              ...\n            </div>\n        </div>\n    )\n}\n19. usePrevious\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useRef } from \"react\"\n\nexport default function usePrevious(value) {\n    const currentRef = useRef(value)\n    const previousRef = useRef()\n    if (currentRef.current !== value) {\n        previousRef.current = currentRef.current\n        currentRef.current = value\n    }\n    return previousRef.current\n}\n\nThe advantages of using usePrevious are remarkable. By using useRef, this hook efficiently stores the current and previous values, updating them whenever the value changes. By comparing the current and previous values, you can easily detect and respond to changes in your component's data.\n\nThis custom hook can be a game-changer in various scenarios. For instance, you can utilize usePrevious to compare and visualize changes in data, track state transitions, or implement undo/redo functionality. Additionally, it can be valuable in form handling, animations, and any situation where having access to the previous value is crucial for your application's logic.\n\nLet's take a glance at how usePrevious can be used in practice. Consider a React component called PreviousComponent, where we have a count state, a name state, and a button to increment the count and change the name. By incorporating usePrevious, we can effortlessly display the current count alongside its previous value, enabling users to visualize the count's changes at a glance.\n\nimport { useState } from \"react\"\nimport usePrevious from \"./usePrevious\"\n\nexport default function PreviousComponent() {\n    const [count, setCount] = useState(0)\n    const [name, setName] = useState(\"Sergey\")\n    const previousCount = usePrevious(count)\n    return (\n        <div>\n            <div>\n                {count} - {previousCount}\n            </div>\n            <div>{name}</div>\n            <button onClick={() => setCount(currentCount => currentCount + 1)}>\n                Increment\n            </button>\n            <button onClick={() => setName(\"John\")}>Change Name</button>\n        </div>\n    )\n}\n20. useRenderCount\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useEffect, useRef } from \"react\"\n\nexport default function useRenderCount() {\n    const count = useRef(1)\n    useEffect(() => count.current++)\n    return count.current\n}\n\nThe useRenderCount hook utilizes React's useEffect and useRef hooks to keep a count of renders. With each render, the count is incremented, providing you with real-time feedback on the component's render frequency.\n\nOne of the major advantages of using useRenderCount is its simplicity. By abstracting the logic into a reusable hook, you can easily integrate it into any component without cluttering your codebase. Additionally, it provides a clear and concise way to monitor render behavior, which can be crucial for performance optimization and debugging.\n\nThis versatile hook can be applied in various scenarios. For instance, when you're developing a complex component that exhibits unexpected rendering patterns, useRenderCount helps you pinpoint the problem by showing the exact number of renders. It is also handy for measuring the impact of certain optimizations or refactoring techniques, allowing you to assess their effectiveness.\n\nimport useRenderCount from \"./useRenderCount\"\nimport useToggle from \"../useToggle/useToggle\"\n\nexport default function RenderCountComponent() {\n    const [boolean, toggle] = useToggle(false)\n    const renderCount = useRenderCount()\n    return (\n        <>\n            <div>{boolean.toString()}</div>\n            <div>{renderCount}</div>\n            <button onClick={toggle}>Toggle</button>\n        </>\n    )\n}\n\nTo get started, simply import the useRenderCount hook and call it within your component. You can see its power in action by checking out the RenderCountComponent example above. By combining useRenderCount with other custom hooks like useToggle, you can build interactive components while keeping an eye on render counts.\n\n21. useScript\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport useAsync from \"../useAsync/useAsync\"\n\nexport default function useScript(url) {\n    return useAsync(() => {\n        const script = document.createElement(\"script\")\n        script.src = url\n        script.async = true\n        return new Promise((resolve, reject) => {\n            script.addEventListener(\"load\", resolve)\n            script.addEventListener(\"error\", reject)\n            document.body.appendChild(script)\n        })\n    }, [url])\n}\n\nOne of the significant advantages of useScript is its ability to handle script loading asynchronously. By setting the script's async attribute to true, you ensure that it won't block the rendering of your application. This improves the performance and overall user experience, especially when dealing with larger scripts or slow network connections.\n\nUseScript can be used in various scenarios. For instance, you can load external libraries like jQuery, enabling you to harness its powerful functionalities without adding bulk to your bundle. Additionally, you can load analytics scripts, social media widgets, or any other script necessary for your application's dynamic behavior.\n\nimport useScript from \"./useScript\"\n\nexport default function ScriptComponent() {\n    const { loading, error } = useScript(\n        \"https://code.jquery.com/jquery-3.6.0.min.js\"\n    )\n    if (loading) return <div>Loading</div>\n    if (error) return <div>Error</div>\n    return <div>{window.$(window).width()}</div>\n}\n\nIn the example above, we see how useScript is utilized in a ScriptComponent. The useScript hook is called with the URL of the jQuery library as an argument. The hook returns the loading and error states, which can be used to display a loading spinner or an error message accordingly. Once the script is successfully loaded, the component displays the current window width using jQuery.\n\n22. useStateWithHistory\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useCallback, useRef, useState } from \"react\"\n\nexport default function useStateWithHistory(\n    defaultValue,\n    { capacity = 10 } = {}\n) {\n    const [value, setValue] = useState(defaultValue)\n    const historyRef = useRef([value])\n    const pointerRef = useRef(0)\n    const set = useCallback(\n        v => {\n            const resolvedValue = typeof v === \"function\" ? v(value) : v\n            if (historyRef.current[pointerRef.current] !== resolvedValue) {\n                if (pointerRef.current < historyRef.current.length - 1) {\n                    historyRef.current.splice(pointerRef.current + 1)\n                }\n                historyRef.current.push(resolvedValue)\n                while (historyRef.current.length > capacity) {\n                    historyRef.current.shift()\n                }\n                pointerRef.current = historyRef.current.length - 1\n            }\n            setValue(resolvedValue)\n        },\n        [capacity, value]\n    )\n    const back = useCallback(() => {\n        if (pointerRef.current <= 0) return\n        pointerRef.current--\n        setValue(historyRef.current[pointerRef.current])\n    }, [])\n    const forward = useCallback(() => {\n        if (pointerRef.current >= historyRef.current.length - 1) return\n        pointerRef.current++\n        setValue(historyRef.current[pointerRef.current])\n    }, [])\n    const go = useCallback(index => {\n        if (index < 0 || index > historyRef.current.length - 1) return\n        pointerRef.current = index\n        setValue(historyRef.current[pointerRef.current])\n    }, [])\n    return [\n        value,\n        set,\n        {\n            history: historyRef.current,\n            pointer: pointerRef.current,\n            back,\n            forward,\n            go,\n        },\n    ]\n}\n\nAdvantages of useStateWithHistory:\n\nAutomatic history tracking: useStateWithHistory automatically keeps track of the values you set, allowing you to access the complete history whenever you need it.\n\nEfficient memory usage: The hook utilizes a capacity parameter, ensuring that the history doesn't grow indefinitely. You can define the maximum number of historical values to keep, preventing excessive memory consumption.\n\nTime-travel functionality: With back(), forward(), and go() functions, you can seamlessly navigate through the recorded history. Travel back and forth between previous states or jump directly to a specific index, enabling powerful undo/redo or step-by-step functionality.\n\nWhere to use useStateWithHistory:\n\nForm management: Simplify the process of handling form inputs by providing an easy way to track changes, revert to previous values, or redo modifications.\n\nUndo/Redo functionality: Implement undo/redo functionality in your application with ease. Track state changes and allow users to navigate back and forth through their actions effortlessly.\n\nStep-by-step navigation: Use useStateWithHistory to build interactive guides or tutorials where users can navigate between different steps while preserving their progress.\n\nimport { useState } from \"react\"\nimport useStateWithHistory from \"./useStateWithHistory\"\n\nexport default function StateWithHistoryComponent() {\n    const [count, setCount, { history, pointer, back, forward, go }] =\n        useStateWithHistory(1)\n    const [name, setName] = useState(\"Sergey\")\n    return (\n        <div>\n            <div>{count}</div>\n            <div>{history.join(\", \")}</div>\n            <div>Pointer - {pointer}</div>\n            <div>{name}</div>\n            <button onClick={() => setCount(currentCount => currentCount * 2)}>\n                Double\n            </button>\n            <button onClick={() => setCount(currentCount => currentCount + 1)}>\n                Increment\n            </button>\n            <button onClick={back}>Back</button>\n            <button onClick={forward}>Forward</button>\n            <button onClick={() => go(2)}>Go To Index 2</button>\n            <button onClick={() => setName(\"John\")}>Change Name</button>\n        </div>\n    )\n}\n23. useStateWithValidation\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useState, useCallback } from \"react\"\n\nexport default function useStateWithValidation(validationFunc, initialValue) {\n    const [state, setState] = useState(initialValue)\n    const [isValid, setIsValid] = useState(() => validationFunc(state))\n    const onChange = useCallback(\n        nextState => {\n            const value =\n                typeof nextState === \"function\" ? nextState(state) : nextState\n            setState(value)\n            setIsValid(validationFunc(value))\n        },\n        [validationFunc]\n    )\n    return [state, onChange, isValid]\n}\n\nThe useStateWithValidation hook combines the useState and useCallback hooks from React to provide an elegant solution. It takes two parameters: a validation function and an initial value. The validation function determines whether the current state is considered valid or not.\n\nOne of the key advantages of this custom hook is its flexibility. You can pass any validation function that suits your specific requirements. Whether it's checking the length of a string, ensuring a numeric value falls within a certain range, or performing more complex validations, useStateWithValidation has got you covered.\n\nimport useStateWithValidation from \"./useStateWithValidation\"\n\nexport default function StateWithValidationComponent() {\n    const [username, setUsername, isValid] = useStateWithValidation(\n        name => name.length > 5,\n        \"\"\n    )\n    return (\n        <>\n            <div>Valid: {isValid.toString()}</div>\n            <input\n                type=\"text\"\n                value={username}\n                onChange={e => setUsername(e.target.value)}\n            />\n        </>\n    )\n}\n\nIn this example, the StateWithValidationComponent uses the useStateWithValidation hook to manage the username state. The validation function checks if the length of the username is greater than 5 characters, and the isValid variable reflects the validity of the current input.\n\n24. useStorage\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useCallback, useState, useEffect } from \"react\"\n\nexport function useLocalStorage(key, defaultValue) {\n    return useStorage(key, defaultValue, window.localStorage)\n}\nexport function useSessionStorage(key, defaultValue) {\n    return useStorage(key, defaultValue, window.sessionStorage)\n}\nfunction useStorage(key, defaultValue, storageObject) {\n    const [value, setValue] = useState(() => {\n        const jsonValue = storageObject.getItem(key)\n        if (jsonValue != null) return JSON.parse(jsonValue)\n        if (typeof defaultValue === \"function\") {\n            return defaultValue()\n        } else {\n            return defaultValue\n        }\n    })\n    useEffect(() => {\n        if (value === undefined) return storageObject.removeItem(key)\n        storageObject.setItem(key, JSON.stringify(value))\n    }, [key, value, storageObject])\n    const remove = useCallback(() => {\n        setValue(undefined)\n    }, [])\n    return [value, setValue, remove]\n}\n\nThe useStorage hook provides two convenient functions: useLocalStorage and useSessionStorage. With useLocalStorage, you can effortlessly store and retrieve data in the browser's local storage, while useSessionStorage offers the same functionality but with the session storage instead.\n\nOne of the key advantages of this custom hook is its simplicity. You can use it to store any type of data, such as strings, numbers, or even complex objects, with just a few lines of code. Additionally, useStorage handles the serialization and deserialization of data for you, so you don't have to worry about converting values to and from JSON.\n\nAnother advantage is the automatic synchronization between the stored data and the component's state. Whenever the stored data changes, the hook updates the component's state accordingly. Similarly, when the component's state changes, the hook automatically persists the new value to the storage. This bidirectional synchronization ensures that your application always reflects the latest data, making it ideal for scenarios where real-time updates are crucial.\n\nThe useStorage hook also provides a remove function, allowing you to easily delete stored values when they are no longer needed. This functionality comes in handy when implementing features like logout buttons or clearing user-specific data.\n\nYou can use the useStorage hook in a variety of scenarios. For example, imagine you have a settings panel where users can customize their preferences. By using useLocalStorage, you can easily store and retrieve these settings, ensuring that they persist across page reloads or even if the user closes and reopens the browser.\n\nimport { useSessionStorage, useLocalStorage } from \"./useStorage\"\n\nexport default function StorageComponent() {\n    const [name, setName, removeName] = useSessionStorage(\"name\", \"Sergey\")\n    const [age, setAge, removeAge] = useLocalStorage(\"age\", 26)\n    return (\n        <div>\n            <div>\n                {name} - {age}\n            </div>\n            <button onClick={() => setName(\"John\")}>Set Name</button>\n            <button onClick={() => setAge(40)}>Set Age</button>\n            <button onClick={removeName}>Remove Name</button>\n            <button onClick={removeAge}>Remove Age</button>\n        </div>\n    )\n}\n25. useTimeout\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useCallback, useEffect, useRef } from \"react\"\n\nexport default function useTimeout(callback, delay) {\n    const callbackRef = useRef(callback)\n    const timeoutRef = useRef()\n    useEffect(() => {\n        callbackRef.current = callback\n    }, [callback])\n    const set = useCallback(() => {\n        timeoutRef.current = setTimeout(() => callbackRef.current(), delay)\n    }, [delay])\n    const clear = useCallback(() => {\n        timeoutRef.current && clearTimeout(timeoutRef.current)\n    }, [])\n    useEffect(() => {\n        set()\n        return clear\n    }, [delay, set, clear])\n    const reset = useCallback(() => {\n        clear()\n        set()\n    }, [clear, set])\n    return { reset, clear }\n}\n\nThe \"useTimeout\" hook encapsulates the logic for setting, clearing, and resetting timeouts within a React component. It takes two parameters: a callback function and a delay duration in milliseconds. Whenever the specified delay elapses, the provided callback function is executed.\n\nOne of the significant advantages of this custom hook is that it ensures the callback function remains up to date even if it changes during component re-renders. By using a useRef to store the callback reference, the hook guarantees that the latest version of the function is always called.\n\nMoreover, the \"useTimeout\" hook optimizes performance by utilizing useCallback to memoize the \"set\" and \"clear\" functions. This means that the functions are only recreated when their dependencies change, preventing unnecessary renders and enhancing efficiency.\n\nThe \"useTimeout\" hook can be utilized in various scenarios where timed actions are required. For example, in a countdown component like the \"TimeoutComponent\" showcased above, you can easily implement a timer that resets after a specific duration. By using the \"useTimeout\" hook, you can effortlessly update the countdown value and manage the timeout without worrying about complex timeout management code.\n\nimport { useState } from \"react\"\nimport useTimeout from \"./useTimeout\"\n\nexport default function TimeoutComponent() {\n    const [count, setCount] = useState(10)\n    const { clear, reset } = useTimeout(() => setCount(0), 1000)\n    return (\n        <div>\n            <div>{count}</div>\n            <button onClick={() => setCount(c => c + 1)}>Increment</button>\n            <button onClick={clear}>Clear Timeout</button>\n            <button onClick={reset}>Reset Timeout</button>\n        </div>\n    )\n}\n26. useToggle\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useState } from \"react\"\n\nexport default function useToggle(defaultValue) {\n    const [value, setValue] = useState(defaultValue)\n    function toggleValue(value) {\n        setValue(currentValue =>\n            typeof value === \"boolean\" ? value : !currentValue\n        )\n    }\n    return [value, toggleValue]\n}\n\nOne of the main advantages of useToggle is its flexibility. With a single line of code, you can initialize the state with a default value. The toggleValue function allows you to easily toggle the state between true and false, or you can pass a boolean value directly to set the state to your desired value. This versatility makes useToggle ideal for a wide range of scenarios where toggling or switching state is required.\n\nUseToggle can be seamlessly integrated into various React components. For instance, in the provided ToggleComponent, the useToggle hook is used to manage the state of a toggle button. With a simple click, the button's state is toggled between true and false. Additionally, the hook provides buttons to directly set the value to true or false, catering to specific use cases. The resulting state is displayed dynamically, allowing for instant feedback.\n\nimport useToggle from \"./useToggle\"\n\nexport default function ToggleComponent() {\n    const [value, toggleValue] = useToggle(false)\n    return (\n        <div>\n            <div>{value.toString()}</div>\n            <button onClick={toggleValue}>Toggle</button>\n            <button onClick={() => toggleValue(true)}>Make True</button>\n            <button onClick={() => toggleValue(false)}>Make False</button>\n        </div>\n    )\n}\n27. useTranslation\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useLocalStorage } from \"../useStorage/useStorage\"\nimport * as translations from \"./translations\"\n\nexport default function useTranslation() {\n    const [language, setLanguage] = useLocalStorage(\"language\", \"en\")\n    const [fallbackLanguage, setFallbackLanguage] = useLocalStorage(\n        \"fallbackLanguage\",\n        \"en\"\n    )\n    const translate = key => {\n        const keys = key.split(\".\")\n        return (\n            getNestedTranslation(language, keys) ??\n            getNestedTranslation(fallbackLanguage, keys) ??\n            key\n        )\n    }\n    return {\n        language,\n        setLanguage,\n        fallbackLanguage,\n        setFallbackLanguage,\n        t: translate,\n    }\n}\nfunction getNestedTranslation(language, keys) {\n    return keys.reduce((obj, key) => {\n        return obj?.[key]\n    }, translations[language])\n}\n\nOne of the key advantages of useTranslation is its seamless integration with the browser's localStorage. It automatically saves the selected language and fallback language preferences, so your users will see the content in their preferred language every time they visit your app.\n\nThe hook utilizes the useLocalStorage hook from the useStorage library to persist the language settings. This ensures that even if the user refreshes the page or navigates away and comes back, their language preference will be preserved.\n\nUsing useTranslation is incredibly straightforward. Simply import the hook and initialize it in your component. You'll have access to the current language, the ability to set the language, the fallback language, and the option to set the fallback language. Additionally, the hook provides a convenient translation function, t, which takes a key as input and returns the corresponding translated value.\n\nYou can use the useTranslation hook in various scenarios. Whether you're building a multi-language website, an internationalized application, or simply need to support translations in your UI components, this hook will simplify the process and make your codebase more maintainable.\n\nimport useTranslation from \"./useTranslation\"\n\nexport default function TranslationComponent() {\n    const { language, setLanguage, setFallbackLanguage, t } = useTranslation()\n    return (\n        <>\n            <div>{language}</div>\n            <div>{t(\"hi\")}</div>\n            <div>{t(\"bye\")}</div>\n            <div>{t(\"nested.value\")}</div>\n            <button onClick={() => setLanguage(\"sp\")}>Change To Spanish</button>\n            <button onClick={() => setLanguage(\"en\")}>Change To English</button>\n            <button onClick={() => setFallbackLanguage(\"sp\")}>Change FB Lang</button>\n        </>\n    )\n}\n28. useUpdateEffect\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useEffect, useRef } from \"react\"\n\nexport default function useUpdateEffect(callback, dependencies) {\n    const firstRenderRef = useRef(true)\n    useEffect(() => {\n        if (firstRenderRef.current) {\n            firstRenderRef.current = false\n            return\n        }\n        return callback()\n    }, dependencies)\n}\n\nThe useUpdateEffect hook is designed to execute a callback function only after the initial render. This behavior is particularly useful when you want to perform actions based on state changes while skipping the initial execution. By leveraging the useRef hook, useUpdateEffect tracks the first render and skips the callback during that phase.\n\nOne of the key advantages of useUpdateEffect is its simplicity. With just a few lines of code, you can enhance your React components by efficiently handling state updates. By specifying the dependencies for the hook, you can control precisely when the callback should be triggered, preventing unnecessary rendering cycles.\n\nThis custom hook can be used in various scenarios. For example, imagine you have a counter component that needs to display an alert every time the count changes, excluding the initial render. By using useUpdateEffect, you can easily achieve this behavior, improving the user experience and reducing unnecessary alerts.\n\nTo implement useUpdateEffect, simply import it into your React component and define the callback function and dependencies. The hook will take care of the rest, ensuring that the callback is executed only when necessary. It's a powerful tool that simplifies state management and enhances the performance of your React applications.\n\nimport { useState } from \"react\"\nimport useUpdateEffect from \"./useUpdateEffect\"\n\nexport default function UpdateEffectComponent() {\n    const [count, setCount] = useState(10)\n    useUpdateEffect(() => alert(count), [count])\n    return (\n        <div>\n            <div>{count}</div>\n            <button onClick={() => setCount(c => c + 1)}>Increment</button>\n        </div>\n    )\n}\n29. useWindowSize\n\nSources: https://github.com/sergeyleschev/react-custom-hooks\n\nimport { useState } from \"react\"\nimport useEventListener from \"../useEventListener/useEventListener\"\n\nexport default function useWindowSize() {\n    const [windowSize, setWindowSize] = useState({\n        width: window.innerWidth,\n        height: window.innerHeight,\n    })\n    useEventListener(\"resize\", () => {\n        setWindowSize({ width: window.innerWidth, height: window.innerHeight })\n    })\n    return windowSize\n}\n\nOne of the main advantages of useWindowSize is its ease of use. By simply importing the hook and invoking it within your functional component, you gain access to an object containing the current width and height of the window. This eliminates the need for boilerplate code and allows you to focus on building dynamic and responsive interfaces.\n\nThe useEventListener hook, also included in this package, intelligently listens for window resize events. Whenever the window size changes, useWindowSize updates the state with the latest dimensions, triggering a re-render of the consuming component. This guarantees that your UI remains in sync with the user's viewing environment, resulting in a more immersive and polished user experience.\n\nThe useWindowSize hook can be used in a variety of scenarios. It's particularly handy when building responsive layouts that adapt to different screen sizes. With this hook, you can effortlessly adjust the styling, layout, or content of your components based on the available window space. Furthermore, it enables you to dynamically render or hide elements, optimize image loading, or perform any other behavior that relies on the window dimensions.\n\nimport useWindowSize from \"./useWindowSize\"\n\nexport default function WindowSizeComponent() {\n    const { width, height } = useWindowSize()\n    return (\n        <div>\n            {width} x {height}\n        </div>\n    )\n}\n\nOnly registered users can participate in poll. Log in, please.\nWhich hooks are more suitable for your project?\n100%\nuseArray\n2\n50%\nuseAsync\n1\n50%\nuseClickOutside\n1\n50%\nuseCookie\n1\n50%\nuseCopyToClipboard\n1\n50%\nuseDarkMode\n1\n50%\nuseDebounce\n1\n50%\nuseDebugInformation\n1\n50%\nuseDeepCompareEffect\n1\n50%\nuseEffectOnce\n1\n50%\nuseEventListener\n1\n50%\nuseFetch\n1\n50%\nuseGeolocation\n1\n50%\nuseHover\n1\n50%\nuseLongPress\n1\n50%\nuseMediaQuery\n1\n50%\nuseOnlineStatus\n1\n50%\nuseOnScreen\n1\n50%\nusePrevious\n1\n50%\nuseRenderCount\n1\n50%\nuseScript\n1\n50%\nuseStateWithHistory\n1\n50%\nuseStateWithValidation\n1\n50%\nuseStorage\n1\n50%\nuseTimeout\n1\n50%\nuseToggle\n1\n50%\nuseTranslation\n1\n50%\nuseUpdateEffect\n1\n50%\nuseWindowSize\n1\n2 users voted. 3 users abstained.\nTags: reactjavascripttypescriptwebdevfrontend\nHubs: Website developmentJavaScriptReactJSTypeScriptVisual programming\n+1\n13\n2\nEditorial Digest\n\nWe email you the best articles monthly\n\n4\nKarma\n5\nRating\nSergey Leschev @Leschev\n\nSystem Architect | Team Lead | CTO\n\nComments 2\nArticles\nTOP OF THE DAY\nSIMILAR ARTICLES\nAVERAGE IT SALARY\n152 006 ₽/mo.\n\n— that’s an average salary for all IT specializations based on 20,753 questionnaires for the 2nd half of 2023. Check if your salary can be higher!\n\n45k\n71k\n97k\n123k\n149k\n175k\n201k\n227k\n253k\n279k\n305k\nCheck your salary\nMOST READING\nReact Custom Hook: useStorage\n841\n0\nGEOMETRY OF SOUND\n277\n0\nMVCC in PostgreSQL-8. Freezing\n4.9K\n0\nReact Custom Hook: useOnScreen\n1.8K\n1\n+1\nBIOS Beep Codes for Troubleshooting\n23K\n1\n+1\n\nYour account\n\nLog in\nSign up\n\nSections\n\nArticles\nNews\nHubs\nAuthors\nSandbox\n\nInformation\n\nHow it works\nFor authors\nFor companies\nDocuments\nAgreement\nConfidential\n\nServices\n\nCorporate blogs\nAdvertising\nNative advertising\nEducation programs\nStartups\nSpecial projects\nLanguage settings\nSupport\n© 2006–2023, Habr", "summary": "该文章讨论了使用自定义的React hooks以及它们在React项目中提高生产力和创新潜力的可能性。作者介绍了超过20个精心设计的hooks，可以用来简化工作流程并提供用户友好的体验。React hooks在React 16.8版本中引入，彻底改变了开发人员在函数组件中编写和管理有状态逻辑的方式。它们允许在多个组件之间重用有状态逻辑，从而产生更清晰、更易维护的代码。\n\n自定义hooks是可重用的函数，以可重用的方式封装复杂逻辑。它们可以通过组合现有的React hooks或其他自定义hooks来创建。自定义hooks遵循使用“use”前缀的命名约定，并且可以封装任何类型的逻辑，例如API调用、表单处理或状态管理。它们促进了代码的可重用性，减少了重复，使开发更高效、可扩展。\n\n文章重点介绍了三个特定的自定义hooks：useArray、useAsync和useClickOutside。useArray hook简化了数组状态的管理，并提供了更清晰、更可读的代码结构。它允许轻松添加、删除、过滤和更新数组中的元素。useAsync hook通过记忆回调函数和管理加载状态来简化异步操作的管理。它是从API获取数据、执行计算或处理表单提交的有价值工具。useClickOutside hook简化了检测指定组件外部点击的过程，使其在实现模态窗口或下拉菜单时非常有用。\n\n总的来说，自定义React hooks为开发人员在React项目中提高生产力、代码可重用性和可维护性提供了强大的工具。通过利用这些hooks，开发人员可以简化代码库、简化异步操作，并提供直观的用户交互。\n\n文章还讨论了其他几个可用于简化React应用程序中特定功能的自定义hooks。useCookie hook简化了cookie管理，并允许在不同会话之间持久化数据。useCopyToClipboard hook简化了将文本复制到剪贴板的过程。useDarkMode hook结合了其他两个hooks，提供了无缝的暗模式体验。useDebounce hook利用useTimeout hook延迟执行回调函数。useDebugInformation hook提供了有关React组件渲染和属性更改的详细信息。useDeepCompareEffect hook允许在useEffect hook中使用深度比较的依赖项。\n\n这些自定义hooks为开发人员在React应用程序中处理常见任务提供了强大且简化的解决方案，节省了宝贵的开发时间并提升了用户体验。\n\n在React中管理依赖关系可能具有挑战性，特别是在处理复杂数据结构或嵌套对象时。然而，useDeepCompareEffect自定义hook提供了解决这个问题的解决方案。它确保只有在依赖项发生深层次变化时才触发effect回调，使用lodash的isEqual函数进行准确比较。useDeepCompareEffect的一个关键优势是它能够防止不必要的重新渲染。通过在当前和先前的依赖项之间进行深度比较，该hook智能地确定是否应该触发effect，从而在浅层比较无法满足要求的情况下实现了优化的性能。这个自定义hook在处理复杂的状态对象时特别有用，比如深层嵌套的数据结构或需要跟踪的多个相互关联的状态。\n\n另一个有用的"}