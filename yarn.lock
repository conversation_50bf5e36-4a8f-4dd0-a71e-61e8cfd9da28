# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@anthropic-ai/sdk@^0.6.2":
  version "0.6.2"
  resolved "http://r.npm.sankuai.com/@anthropic-ai/sdk/download/@anthropic-ai/sdk-0.6.2.tgz#4be415e6b1d948df6f8e03af84aedf102ec74b70"
  integrity sha1-S+QV5rHZSN9vjgOvhK7fEC7HS3A=
  dependencies:
    "@types/node" "^18.11.18"
    "@types/node-fetch" "^2.6.4"
    abort-controller "^3.0.0"
    agentkeepalive "^4.2.1"
    digest-fetch "^1.3.0"
    form-data-encoder "1.7.2"
    formdata-node "^4.3.2"
    node-fetch "^2.6.7"

"@babel/runtime@^7.7.2":
  version "7.23.2"
  resolved "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.23.2.tgz#062b0ac103261d68a966c4c7baf2ae3e62ec3885"
  integrity sha1-BisKwQMmHWipZsTHuvKuPmLsOIU=
  dependencies:
    regenerator-runtime "^0.14.0"

"@jimp/bmp@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/bmp/download/@jimp/bmp-0.14.0.tgz#6df246026554f276f7b354047c6fff9f5b2b5182"
  integrity sha1-bfJGAmVU8nb3s1QEfG//n1srUYI=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"
    bmp-js "^0.1.0"

"@jimp/bmp@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/bmp/download/@jimp/bmp-0.16.13.tgz#57ffa5b17417b5a181f6f184bdabc8218e8448ef"
  integrity sha1-V/+lsXQXtaGB9vGEvavIIY6ESO8=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"
    bmp-js "^0.1.0"

"@jimp/core@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/core/download/@jimp/core-0.14.0.tgz#870c9ca25b40be353ebda1d2abb48723d9010055"
  integrity sha1-hwycoltAvjU+vaHSq7SHI9kBAFU=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"
    any-base "^1.1.0"
    buffer "^5.2.0"
    exif-parser "^0.1.12"
    file-type "^9.0.0"
    load-bmfont "^1.3.1"
    mkdirp "^0.5.1"
    phin "^2.9.1"
    pixelmatch "^4.0.2"
    tinycolor2 "^1.4.1"

"@jimp/core@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/core/download/@jimp/core-0.16.13.tgz#7171745a912b5b847f8bf53e70b0672c5ca92744"
  integrity sha1-cXF0WpErW4R/i/U+cLBnLFypJ0Q=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"
    any-base "^1.1.0"
    buffer "^5.2.0"
    exif-parser "^0.1.12"
    file-type "^16.5.4"
    load-bmfont "^1.3.1"
    mkdirp "^0.5.1"
    phin "^2.9.1"
    pixelmatch "^4.0.2"
    tinycolor2 "^1.4.1"

"@jimp/custom@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/custom/download/@jimp/custom-0.14.0.tgz#1dbbf0094df7403f4e03bc984ed92e7458842f74"
  integrity sha1-HbvwCU33QD9OA7yYTtkudFiEL3Q=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/core" "^0.14.0"

"@jimp/custom@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/custom/download/@jimp/custom-0.16.13.tgz#2e4ed447b7410b81fe9103682b4166af904daf84"
  integrity sha1-Lk7UR7dBC4H+kQNoK0Fmr5BNr4Q=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/core" "^0.16.13"

"@jimp/gif@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/gif/download/@jimp/gif-0.14.0.tgz#db159f57c3cfd1566bbe8b124958791998614960"
  integrity sha1-2xWfV8PP0VZrvosSSVh5GZhhSWA=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"
    gifwrap "^0.9.2"
    omggif "^1.0.9"

"@jimp/gif@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/gif/download/@jimp/gif-0.16.13.tgz#fa72f35d8ad67d6ce3a3d7ef6c8d04a462afaaf9"
  integrity sha1-+nLzXYrWfWzjo9fvbI0EpGKvqvk=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"
    gifwrap "^0.9.2"
    omggif "^1.0.9"

"@jimp/jpeg@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/jpeg/download/@jimp/jpeg-0.14.0.tgz#8a687a6a653bbbae38c522edef8f84bb418d9461"
  integrity sha1-imh6amU7u644xSLt74+Eu0GNlGE=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"
    jpeg-js "^0.4.0"

"@jimp/jpeg@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/jpeg/download/@jimp/jpeg-0.16.13.tgz#e1c128a591bd7f8a26c8731fd0bc65d32d4ba32a"
  integrity sha1-4cEopZG9f4omyHMf0Lxl0y1Loyo=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"
    jpeg-js "^0.4.2"

"@jimp/plugin-blit@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-blit/download/@jimp/plugin-blit-0.14.0.tgz#5eb374be1201313b2113899fb842232d8fcfd345"
  integrity sha1-XrN0vhIBMTshE4mfuEIjLY/P00U=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-blit@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-blit/download/@jimp/plugin-blit-0.16.13.tgz#370303edef02b75aa3e316726c5a3aac3e92f5d0"
  integrity sha1-NwMD7e8Ct1qj4xZybFo6rD6S9dA=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-blur@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-blur/download/@jimp/plugin-blur-0.14.0.tgz#fe07e4932d5a2f5d8c9831e245561553224bfc60"
  integrity sha1-/gfkky1aL12MmDHiRVYVUyJL/GA=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-blur@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-blur/download/@jimp/plugin-blur-0.16.13.tgz#27b82295a3dee88d6e029d4d62f5de8118b845e6"
  integrity sha1-J7gilaPe6I1uAp1NYvXegRi4ReY=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-circle@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-circle/download/@jimp/plugin-circle-0.14.0.tgz#****************************************"
  integrity sha1-gsDpBKNOkPpnL7nChryJLpIIjd8=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-circle@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-circle/download/@jimp/plugin-circle-0.16.13.tgz#****************************************"
  integrity sha1-169hqVsX5nx/1DYc0dWI4AtYtrY=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-color@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-color/download/@jimp/plugin-color-0.14.0.tgz#772bd2d80a88bc66ea1331d010207870f169a74b"
  integrity sha1-dyvS2AqIvGbqEzHQECB4cPFpp0s=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"
    tinycolor2 "^1.4.1"

"@jimp/plugin-color@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-color/download/@jimp/plugin-color-0.16.13.tgz#825227e7e6f32d227740ad1bd97c389083c1d0d1"
  integrity sha1-glIn5+bzLSJ3QK0b2Xw4kIPB0NE=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"
    tinycolor2 "^1.4.1"

"@jimp/plugin-contain@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-contain/download/@jimp/plugin-contain-0.14.0.tgz#c68115420d182e696f81bbe76fb5e704909b2b6a"
  integrity sha1-xoEVQg0YLmlvgbvnb7XnBJCbK2o=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-contain@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-contain/download/@jimp/plugin-contain-0.16.13.tgz#7a42ed1ce580bf910f812ba2f35e0fa2cfe501ac"
  integrity sha1-ekLtHOWAv5EPgSui814Pos/lAaw=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-cover@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-cover/download/@jimp/plugin-cover-0.14.0.tgz#4755322589c5885e44e14e31b86b542e907297ce"
  integrity sha1-R1UyJYnFiF5E4U4xuGtULpByl84=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-cover@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-cover/download/@jimp/plugin-cover-0.16.13.tgz#9c964be05b163e0f0e06866a9afcebe775dff246"
  integrity sha1-nJZL4FsWPg8OBoZqmvzr53Xf8kY=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-crop@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-crop/download/@jimp/plugin-crop-0.14.0.tgz#4cbd856ca84ffc37230fad2534906f2f75aa3057"
  integrity sha1-TL2FbKhP/DcjD60lNJBvL3WqMFc=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-crop@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-crop/download/@jimp/plugin-crop-0.16.13.tgz#80c6ae4d401a8de6cc11b265f3cdecd80425b9a9"
  integrity sha1-gMauTUAajebMEbJl883s2AQluak=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-displace@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-displace/download/@jimp/plugin-displace-0.14.0.tgz#b0e6a57d00cb1f893f541413fe9d737d23c3b70c"
  integrity sha1-sOalfQDLH4k/VBQT/p1zfSPDtww=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-displace@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-displace/download/@jimp/plugin-displace-0.16.13.tgz#fd72aa93b3fe97a1c3da729e6b26399661ce8ce5"
  integrity sha1-/XKqk7P+l6HD2nKeayY5lmHOjOU=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-dither@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-dither/download/@jimp/plugin-dither-0.14.0.tgz#9185ec4c38e02edc9e5831f5d709f6ba891e1b93"
  integrity sha1-kYXsTDjgLtyeWDH11wn2uokeG5M=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-dither@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-dither/download/@jimp/plugin-dither-0.16.13.tgz#430750f73d528df7ebe21bb508fb80f9f515305d"
  integrity sha1-QwdQ9z1Sjffr4hu1CPuA+fUVMF0=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-fisheye@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-fisheye/download/@jimp/plugin-fisheye-0.14.0.tgz#9f26346cf2fbc660cc2008cd7fd30a83b5029e78"
  integrity sha1-nyY0bPL7xmDMIAjNf9MKg7UCnng=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-fisheye@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-fisheye/download/@jimp/plugin-fisheye-0.16.13.tgz#caf69851ab25c44d13c952880a8e43c928abd3f1"
  integrity sha1-yvaYUaslxE0TyVKICo5DySir0/E=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-flip@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-flip/download/@jimp/plugin-flip-0.14.0.tgz#7966d6aa3b5fe1aa4d2d561ff12b8ef5ccb9b071"
  integrity sha1-eWbWqjtf4apNLVYf8SuO9cy5sHE=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-flip@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-flip/download/@jimp/plugin-flip-0.16.13.tgz#3dd167e14d03d62410c519990728ac3c247c0692"
  integrity sha1-PdFn4U0D1iQQxRmZByisPCR8BpI=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-gaussian@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-gaussian/download/@jimp/plugin-gaussian-0.14.0.tgz#452bc1971a4467ad9b984aa67f4c200bf941bb65"
  integrity sha1-RSvBlxpEZ62bmEqmf0wgC/lBu2U=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-gaussian@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-gaussian/download/@jimp/plugin-gaussian-0.16.13.tgz#79879d9371aff3e1714c54be0771418573ac2954"
  integrity sha1-eYedk3Gv8+FxTFS+B3FBhXOsKVQ=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-invert@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-invert/download/@jimp/plugin-invert-0.14.0.tgz#cd31a555860e9f821394936d15af161c09c42921"
  integrity sha1-zTGlVYYOn4ITlJNtFa8WHAnEKSE=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-invert@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-invert/download/@jimp/plugin-invert-0.16.13.tgz#7449283d5b0f405ce2cd1b93a6d79169c970e431"
  integrity sha1-dEkoPVsPQFzizRuTpteRaclw5DE=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-mask@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-mask/download/@jimp/plugin-mask-0.14.0.tgz#52619643ac6222f85e6b27dee33c771ca3a6a4c9"
  integrity sha1-UmGWQ6xiIvheayfe4zx3HKOmpMk=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-mask@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-mask/download/@jimp/plugin-mask-0.16.13.tgz#70b4bef4a598e41571f9a3e0c33fcc730eeae24d"
  integrity sha1-cLS+9KWY5BVx+aPgwz/Mcw7q4k0=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-normalize@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-normalize/download/@jimp/plugin-normalize-0.14.0.tgz#bf39e356b6d473f582ce95633ad49c9cdb82492b"
  integrity sha1-vznjVrbUc/WCzpVjOtScnNuCSSs=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-normalize@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-normalize/download/@jimp/plugin-normalize-0.16.13.tgz#fd7c802c3f6be8d34abf0dbeadfe1d783e531d67"
  integrity sha1-/XyALD9r6NNKvw2+rf4deD5THWc=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-print@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-print/download/@jimp/plugin-print-0.14.0.tgz#1c43c2a92a7adc05b464863882cb89ce486d63e6"
  integrity sha1-HEPCqSp63AW0ZIY4gsuJzkhtY+Y=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"
    load-bmfont "^1.4.0"

"@jimp/plugin-print@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-print/download/@jimp/plugin-print-0.16.13.tgz#595fb6db6677ac3d2b6bfe7144658019791bf288"
  integrity sha1-WV+222Z3rD0ra/5xRGWAGXkb8og=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"
    load-bmfont "^1.4.0"

"@jimp/plugin-resize@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-resize/download/@jimp/plugin-resize-0.14.0.tgz#ef7fc6c2e45f8bcab62456baf8fd3bc415b02b64"
  integrity sha1-73/GwuRfi8q2JFa6+P07xBWwK2Q=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-resize@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-resize/download/@jimp/plugin-resize-0.16.13.tgz#6267087f724d47e7bb8824c5b842d9315f50b8e7"
  integrity sha1-YmcIf3JNR+e7iCTFuELZMV9QuOc=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-rotate@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-rotate/download/@jimp/plugin-rotate-0.14.0.tgz#3632bc159bf1c3b9ec9f459d9c05d02a11781ee7"
  integrity sha1-NjK8FZvxw7nsn0WdnAXQKhF4Huc=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-rotate@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-rotate/download/@jimp/plugin-rotate-0.16.13.tgz#9981f24631b1a0ad486d2b75a0163918ff912491"
  integrity sha1-mYHyRjGxoK1IbSt1oBY5GP+RJJE=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-scale@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-scale/download/@jimp/plugin-scale-0.14.0.tgz#d30f0cd1365b8e68f43fa423300ae7f124e9bf10"
  integrity sha1-0w8M0TZbjmj0P6QjMArn8STpvxA=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-scale@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-scale/download/@jimp/plugin-scale-0.16.13.tgz#36b1b7d70819591901339926a91dae4864cc1b92"
  integrity sha1-NrG31wgZWRkBM5kmqR2uSGTMG5I=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-shadow@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-shadow/download/@jimp/plugin-shadow-0.14.0.tgz#471fdb9f109ff2d9e20d533d45e1e18e0b48c749"
  integrity sha1-Rx/bnxCf8tniDVM9ReHhjgtIx0k=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-shadow@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-shadow/download/@jimp/plugin-shadow-0.16.13.tgz#f5b58122c0a6e1307efcddfc165ce1291415d553"
  integrity sha1-9bWBIsCm4TB+/N38FlzhKRQV1VM=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugin-threshold@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-threshold/download/@jimp/plugin-threshold-0.14.0.tgz#ebd72721c7d1d518c5bb6e494e55d97ac3351d3b"
  integrity sha1-69cnIcfR1RjFu25JTlXZesM1HTs=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"

"@jimp/plugin-threshold@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugin-threshold/download/@jimp/plugin-threshold-0.16.13.tgz#8de7500b03342b251201bc0feb84955dd3e410f0"
  integrity sha1-jedQCwM0KyUSAbwP64SVXdPkEPA=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"

"@jimp/plugins@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/plugins/download/@jimp/plugins-0.14.0.tgz#41dba85f15ab8dadb4162100eb54e5f27b93ee2c"
  integrity sha1-QduoXxWrja20FiEA61Tl8nuT7iw=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/plugin-blit" "^0.14.0"
    "@jimp/plugin-blur" "^0.14.0"
    "@jimp/plugin-circle" "^0.14.0"
    "@jimp/plugin-color" "^0.14.0"
    "@jimp/plugin-contain" "^0.14.0"
    "@jimp/plugin-cover" "^0.14.0"
    "@jimp/plugin-crop" "^0.14.0"
    "@jimp/plugin-displace" "^0.14.0"
    "@jimp/plugin-dither" "^0.14.0"
    "@jimp/plugin-fisheye" "^0.14.0"
    "@jimp/plugin-flip" "^0.14.0"
    "@jimp/plugin-gaussian" "^0.14.0"
    "@jimp/plugin-invert" "^0.14.0"
    "@jimp/plugin-mask" "^0.14.0"
    "@jimp/plugin-normalize" "^0.14.0"
    "@jimp/plugin-print" "^0.14.0"
    "@jimp/plugin-resize" "^0.14.0"
    "@jimp/plugin-rotate" "^0.14.0"
    "@jimp/plugin-scale" "^0.14.0"
    "@jimp/plugin-shadow" "^0.14.0"
    "@jimp/plugin-threshold" "^0.14.0"
    timm "^1.6.1"

"@jimp/plugins@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/plugins/download/@jimp/plugins-0.16.13.tgz#cf441ee13204dd9474bc0e67e41c50afc910de4f"
  integrity sha1-z0Qe4TIE3ZR0vA5n5BxQr8kQ3k8=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/plugin-blit" "^0.16.13"
    "@jimp/plugin-blur" "^0.16.13"
    "@jimp/plugin-circle" "^0.16.13"
    "@jimp/plugin-color" "^0.16.13"
    "@jimp/plugin-contain" "^0.16.13"
    "@jimp/plugin-cover" "^0.16.13"
    "@jimp/plugin-crop" "^0.16.13"
    "@jimp/plugin-displace" "^0.16.13"
    "@jimp/plugin-dither" "^0.16.13"
    "@jimp/plugin-fisheye" "^0.16.13"
    "@jimp/plugin-flip" "^0.16.13"
    "@jimp/plugin-gaussian" "^0.16.13"
    "@jimp/plugin-invert" "^0.16.13"
    "@jimp/plugin-mask" "^0.16.13"
    "@jimp/plugin-normalize" "^0.16.13"
    "@jimp/plugin-print" "^0.16.13"
    "@jimp/plugin-resize" "^0.16.13"
    "@jimp/plugin-rotate" "^0.16.13"
    "@jimp/plugin-scale" "^0.16.13"
    "@jimp/plugin-shadow" "^0.16.13"
    "@jimp/plugin-threshold" "^0.16.13"
    timm "^1.6.1"

"@jimp/png@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/png/download/@jimp/png-0.14.0.tgz#0f2dddb5125c0795ca7e67c771204c5437fcda4b"
  integrity sha1-Dy3dtRJcB5XKfmfHcSBMVDf82ks=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.14.0"
    pngjs "^3.3.3"

"@jimp/png@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/png/download/@jimp/png-0.16.13.tgz#8b130cc5e1e754c074c42fa3fe2609897cefdf7c"
  integrity sha1-ixMMxeHnVMB0xC+j/iYJiXzv33w=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/utils" "^0.16.13"
    pngjs "^3.3.3"

"@jimp/tiff@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/tiff/download/@jimp/tiff-0.14.0.tgz#a5b25bbe7c43fc3b07bad4e2ab90e0e164c1967f"
  integrity sha1-pbJbvnxD/DsHutTiq5Dg4WTBln8=
  dependencies:
    "@babel/runtime" "^7.7.2"
    utif "^2.0.1"

"@jimp/tiff@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/tiff/download/@jimp/tiff-0.16.13.tgz#9cf8d19f2b0b0c46758e81acfc7d656835ee6da1"
  integrity sha1-nPjRnysLDEZ1joGs/H1laDXubaE=
  dependencies:
    "@babel/runtime" "^7.7.2"
    utif "^2.0.1"

"@jimp/types@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/types/download/@jimp/types-0.14.0.tgz#ef681ff702883c5f105b5e4e30d49abf39ee9e34"
  integrity sha1-72gf9wKIPF8QW15OMNSavznunjQ=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/bmp" "^0.14.0"
    "@jimp/gif" "^0.14.0"
    "@jimp/jpeg" "^0.14.0"
    "@jimp/png" "^0.14.0"
    "@jimp/tiff" "^0.14.0"
    timm "^1.6.1"

"@jimp/types@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/types/download/@jimp/types-0.16.13.tgz#39be1886cbfa4fb5e77e17441a046a1f961d3046"
  integrity sha1-Ob4Yhsv6T7XnfhdEGgRqH5YdMEY=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/bmp" "^0.16.13"
    "@jimp/gif" "^0.16.13"
    "@jimp/jpeg" "^0.16.13"
    "@jimp/png" "^0.16.13"
    "@jimp/tiff" "^0.16.13"
    timm "^1.6.1"

"@jimp/utils@^0.14.0":
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/@jimp/utils/download/@jimp/utils-0.14.0.tgz#296254e63118554c62c31c19ac6b8c4bfe6490e5"
  integrity sha1-KWJU5jEYVUxiwxwZrGuMS/5kkOU=
  dependencies:
    "@babel/runtime" "^7.7.2"
    regenerator-runtime "^0.13.3"

"@jimp/utils@^0.16.13":
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/@jimp/utils/download/@jimp/utils-0.16.13.tgz#afde41b9c6cdadfb45d83cb5e16deb65f369bf99"
  integrity sha1-r95BucbNrftF2Dy14W3rZfNpv5k=
  dependencies:
    "@babel/runtime" "^7.7.2"
    regenerator-runtime "^0.13.3"

"@nibfe/adapi@^1.2.3":
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/@nibfe/adapi/download/@nibfe/adapi-1.2.3.tgz#e16a574911da5a434f21b068f4825e68817972d3"
  integrity sha1-4WpXSRHaWkNPIbBo9IJeaIF5ctM=
  dependencies:
    bufferutil "4.0.7"
    core-js "^3.8.0"
    crypto-js "4.1.1"
    openai "3.3.0"
    utf-8-validate "6.0.3"
    ws "8.13.0"

"@sindresorhus/is@^5.2.0":
  version "5.6.0"
  resolved "http://r.npm.sankuai.com/@sindresorhus/is/download/@sindresorhus/is-5.6.0.tgz#41dd6093d34652cddb5d5bdeee04eafc33826668"
  integrity sha1-Qd1gk9NGUs3bXVve7gTq/DOCZmg=

"@szmarczak/http-timer@^5.0.1":
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/@szmarczak/http-timer/download/@szmarczak/http-timer-5.0.1.tgz#c7c1bf1141cdd4751b0399c8fc7b8b664cd5be3a"
  integrity sha1-x8G/EUHN1HUbA5nI/HuLZkzVvjo=
  dependencies:
    defer-to-connect "^2.0.1"

"@tokenizer/token@^0.3.0":
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/@tokenizer/token/download/@tokenizer/token-0.3.0.tgz#fe98a93fe789247e998c75e74e9c7c63217aa276"
  integrity sha1-/pipP+eJJH6ZjHXnTpx8YyF6onY=

"@types/http-cache-semantics@^4.0.1":
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/@types/http-cache-semantics/download/@types/http-cache-semantics-4.0.2.tgz#abe102d06ccda1efdf0ed98c10ccf7f36a785a41"
  integrity sha1-q+EC0GzNoe/fDtmMEMz382p4WkE=

"@types/node-fetch@^2.6.4":
  version "2.6.6"
  resolved "http://r.npm.sankuai.com/@types/node-fetch/download/@types/node-fetch-2.6.6.tgz#b72f3f4bc0c0afee1c0bc9cff68e041d01e3e779"
  integrity sha1-ty8/S8DAr+4cC8nP9o4EHQHj53k=
  dependencies:
    "@types/node" "*"
    form-data "^4.0.0"

"@types/node@*":
  version "20.8.6"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-20.8.6.tgz#0dbd4ebcc82ad0128df05d0e6f57e05359ee47fa"
  integrity sha1-Db1OvMgq0BKN8F0Ob1fgU1nuR/o=
  dependencies:
    undici-types "~5.25.1"

"@types/node@16.9.1":
  version "16.9.1"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-16.9.1.tgz#0611b37db4246c937feef529ddcc018cf8e35708"
  integrity sha1-BhGzfbQkbJN/7vUp3cwBjPjjVwg=

"@types/node@^18.11.18":
  version "18.18.5"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-18.18.5.tgz#afc0fd975df946d6e1add5bbf98264225b212244"
  integrity sha1-r8D9l135RtbhrdW7+YJkIlshIkQ=

"@xmldom/xmldom@^0.8.6", "@xmldom/xmldom@^0.8.8":
  version "0.8.10"
  resolved "http://r.npm.sankuai.com/@xmldom/xmldom/download/@xmldom/xmldom-0.8.10.tgz#a1337ca426aa61cef9fe15b5b28e340a72f6fa99"
  integrity sha1-oTN8pCaqYc75/hW1so40CnL2+pk=

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/abort-controller/download/abort-controller-3.0.0.tgz#eaf54d53b62bae4138e809ca225c8439a6efb392"
  integrity sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=
  dependencies:
    event-target-shim "^5.0.0"

agentkeepalive@^4.2.1:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/agentkeepalive/download/agentkeepalive-4.5.0.tgz#2673ad1389b3c418c5a20c5d7364f93ca04be923"
  integrity sha1-JnOtE4mzxBjFogxdc2T5PKBL6SM=
  dependencies:
    humanize-ms "^1.2.1"

ajv-keywords@^3.1.0:
  version "3.5.2"
  resolved "http://r.npm.sankuai.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz#31f29da5ab6e00d1c2d329acf7b5929614d5014d"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv@^6.1.0:
  version "6.12.6"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-colors@^4.1.3:
  version "4.1.3"
  resolved "http://r.npm.sankuai.com/ansi-colors/download/ansi-colors-4.1.3.tgz#37611340eb2243e70cc604cad35d63270d48781b"
  integrity sha1-N2ETQOsiQ+cMxgTK011jJw1IeBs=

ansi-escapes@^4.3.0:
  version "4.3.2"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz#6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-escapes@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-5.0.0.tgz#b6a0caf0eef0c41af190e9a749e0c00ec04bb2a6"
  integrity sha1-tqDK8O7wxBrxkOmnSeDADsBLsqY=
  dependencies:
    type-fest "^1.0.2"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

any-base@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/any-base/download/any-base-1.1.0.tgz#ae101a62bc08a597b4c9ab5b7089d456630549fe"
  integrity sha1-rhAaYrwIpZe0yatbcInUVmMFSf4=

app-path@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/app-path/download/app-path-4.0.0.tgz#86a6940f96d5cbd9e09e510f759b9d73b7a144ca"
  integrity sha1-hqaUD5bVy9ngnlEPdZudc7ehRMo=
  dependencies:
    execa "^5.0.0"

argparse@~1.0.3:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

array-range@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/array-range/download/array-range-1.0.1.tgz#f56e46591843611c6a56f77ef02eda7c50089bfc"
  integrity sha1-9W5GWRhDYRxqVvd+8C7afFAIm/w=

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/astral-regex/download/astral-regex-2.0.0.tgz#483143c567aeed4785759c0865786dc77d7d2e31"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

axios@^0.26.0:
  version "0.26.1"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.26.1.tgz#1ede41c51fcf51bbbd6fd43669caaa4f0495aaa9"
  integrity sha1-Ht5BxR/PUbu9b9Q2acqqTwSVqqk=
  dependencies:
    follow-redirects "^1.14.8"

base-64@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/base-64/download/base-64-0.1.0.tgz#780a99c84e7d600260361511c4877613bf24f6bb"
  integrity sha1-eAqZyE59YAJgNhURxId2E78k9rs=

base64-js@^1.3.1, base64-js@^1.5.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/base64-js/download/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

big.js@^5.2.2:
  version "5.2.2"
  resolved "http://r.npm.sankuai.com/big.js/download/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

bluebird@~3.4.0:
  version "3.4.7"
  resolved "http://r.npm.sankuai.com/bluebird/download/bluebird-3.4.7.tgz#f72d760be09b7f76d08ed8fae98b289a8d05fab3"
  integrity sha1-9y12C+Cbf3bQjtj66Ysomo0F+rM=

bmp-js@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/bmp-js/download/bmp-js-0.1.0.tgz#e05a63f796a6c1ff25f4771ec7adadc148c07233"
  integrity sha1-4Fpj95amwf8l9Hcex62twUjAcjM=

buffer-equal@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/buffer-equal/download/buffer-equal-0.0.1.tgz#91bc74b11ea405bc916bc6aa908faafa5b4aac4b"
  integrity sha1-kbx0sR6kBbyRa8aqkI+q+ltKrEs=

buffer@^5.2.0:
  version "5.7.1"
  resolved "http://r.npm.sankuai.com/buffer/download/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

bufferutil@4.0.7:
  version "4.0.7"
  resolved "http://r.npm.sankuai.com/bufferutil/download/bufferutil-4.0.7.tgz#60c0d19ba2c992dd8273d3f73772ffc894c153ad"
  integrity sha1-YMDRm6LJkt2Cc9P3N3L/yJTBU60=
  dependencies:
    node-gyp-build "^4.3.0"

cacheable-lookup@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/cacheable-lookup/download/cacheable-lookup-7.0.0.tgz#3476a8215d046e5a3202a9209dd13fec1f933a27"
  integrity sha1-NHaoIV0EbloyAqkgndE/7B+TOic=

cacheable-request@^10.2.8:
  version "10.2.13"
  resolved "http://r.npm.sankuai.com/cacheable-request/download/cacheable-request-10.2.13.tgz#b7012bb4a2acdb18cb54d2dff751d766b3500842"
  integrity sha1-twErtKKs2xjLVNLf91HXZrNQCEI=
  dependencies:
    "@types/http-cache-semantics" "^4.0.1"
    get-stream "^6.0.1"
    http-cache-semantics "^4.1.1"
    keyv "^4.5.3"
    mimic-response "^4.0.0"
    normalize-url "^8.0.0"
    responselike "^3.0.0"

chalk@^4.1.1:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

charenc@0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/charenc/download/charenc-0.0.2.tgz#c0a1d2f3a7092e03774bfa83f14c0fc5790a8667"
  integrity sha1-wKHS86cJLgN3S/qD8UwPxXkKhmc=

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

core-js@^3.8.0:
  version "3.33.0"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-3.33.0.tgz#70366dbf737134761edb017990cf5ce6c6369c40"
  integrity sha1-cDZtv3NxNHYe2wF5kM9c5sY2nEA=

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.3.tgz#f73a85b9d5d41d045551c177e2882d4ac85728a6"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypt@0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/crypt/download/crypt-0.0.2.tgz#88d7ff7ec0dfb86f713dc87bbb42d044d3e6c41b"
  integrity sha1-iNf/fsDfuG9xPch7u0LQRNPmxBs=

crypto-js@4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/crypto-js/download/crypto-js-4.1.1.tgz#9e485bcf03521041bd85844786b83fb7619736cf"
  integrity sha1-nkhbzwNSEEG9hYRHhrg/t2GXNs8=

cycled@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/cycled/download/cycled-1.2.0.tgz#dde6d9d58b0b14d02c8138c8f55be416a6595baf"
  integrity sha1-3ebZ1YsLFNAsgTjI9VvkFqZZW68=

decode-gif@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/decode-gif/download/decode-gif-1.0.1.tgz#7630bf5b2f2a6d6797c487569cb772646c32752b"
  integrity sha1-djC/Wy8qbWeXxIdWnLdyZGwydSs=
  dependencies:
    array-range "^1.0.1"
    omggif "^1.0.10"

decompress-response@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/decompress-response/download/decompress-response-6.0.0.tgz#ca387612ddb7e104bd16d85aab00d5ecf09c66fc"
  integrity sha1-yjh2Et234QS9FthaqwDV7PCcZvw=
  dependencies:
    mimic-response "^3.1.0"

defer-to-connect@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/defer-to-connect/download/defer-to-connect-2.0.1.tgz#8016bdb4143e4632b77a3449c6236277de520587"
  integrity sha1-gBa9tBQ+RjK3ejRJxiNid95SBYc=

delay@^4.3.0:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/delay/download/delay-4.4.1.tgz#6e02d02946a1b6ab98b39262ced965acba2ac4d1"
  integrity sha1-bgLQKUahtquYs5JiztllrLoqxNE=

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

digest-fetch@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/digest-fetch/download/digest-fetch-1.3.0.tgz#898e69264d00012a23cf26e8a3e40320143fc661"
  integrity sha1-iY5pJk0AASojzyboo+QDIBQ/xmE=
  dependencies:
    base-64 "^0.1.0"
    md5 "^2.3.0"

dingbat-to-unicode@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/dingbat-to-unicode/download/dingbat-to-unicode-1.0.1.tgz#5091dd673241453e6b5865e26e5a4452cdef5c83"
  integrity sha1-UJHdZzJBRT5rWGXiblpEUs3vXIM=

dom-walk@^0.1.0:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/dom-walk/download/dom-walk-0.1.2.tgz#0c548bef048f4d1f2a97249002236060daa3fd84"
  integrity sha1-DFSL7wSPTR8qlySQAiNgYNqj/YQ=

draftlog@^1.0.13:
  version "1.0.13"
  resolved "http://r.npm.sankuai.com/draftlog/download/draftlog-1.0.13.tgz#2a415783955dbc6e2bb3b51a93c3d559f26b453d"
  integrity sha1-KkFXg5VdvG4rs7Uak8PVWfJrRT0=

duck@^0.1.12:
  version "0.1.12"
  resolved "http://r.npm.sankuai.com/duck/download/duck-0.1.12.tgz#de7adf758421230b6d7aee799ce42670586b9efa"
  integrity sha1-3nrfdYQhIwtteu55nOQmcFhrnvo=
  dependencies:
    underscore "^1.13.1"

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/emojis-list/download/emojis-list-3.0.0.tgz#5570662046ad29e2e916e71aae260abdff4f6a78"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/event-target-shim/download/event-target-shim-5.0.1.tgz#5d4d3ebdf9583d63a5333ce2deb7480ab2b05789"
  integrity sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=

execa@^5.0.0:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/execa/download/execa-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exif-parser@^0.1.12:
  version "0.1.12"
  resolved "http://r.npm.sankuai.com/exif-parser/download/exif-parser-0.1.12.tgz#58a9d2d72c02c1f6f02a0ef4a9166272b7760922"
  integrity sha1-WKnS1ywCwfbwKg70qRZicrd2CSI=

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

file-type@^16.5.4:
  version "16.5.4"
  resolved "http://r.npm.sankuai.com/file-type/download/file-type-16.5.4.tgz#474fb4f704bee427681f98dd390058a172a6c2fd"
  integrity sha1-R0+09wS+5CdoH5jdOQBYoXKmwv0=
  dependencies:
    readable-web-to-node-stream "^3.0.0"
    strtok3 "^6.2.4"
    token-types "^4.1.1"

file-type@^9.0.0:
  version "9.0.0"
  resolved "http://r.npm.sankuai.com/file-type/download/file-type-9.0.0.tgz#a68d5ad07f486414dfb2c8866f73161946714a18"
  integrity sha1-po1a0H9IZBTfssiGb3MWGUZxShg=

follow-redirects@^1.14.8:
  version "1.15.2"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.2.tgz#b460864144ba63f2681096f274c4e57026da2c13"
  integrity sha1-tGCGQUS6Y/JoEJbydMTlcCbaLBM=

form-data-encoder@1.7.2:
  version "1.7.2"
  resolved "http://r.npm.sankuai.com/form-data-encoder/download/form-data-encoder-1.7.2.tgz#1f1ae3dccf58ed4690b86d87e4f57c654fbab040"
  integrity sha1-Hxrj3M9Y7UaQuG2H5PV8ZU+6sEA=

form-data-encoder@^2.1.2:
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/form-data-encoder/download/form-data-encoder-2.1.4.tgz#261ea35d2a70d48d30ec7a9603130fa5515e9cd5"
  integrity sha1-Jh6jXSpw1I0w7HqWAxMPpVFenNU=

form-data@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  integrity sha1-k5Gdrq82HuUpWEubMWZNwSyfpFI=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

formdata-node@^4.3.2:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/formdata-node/download/formdata-node-4.4.1.tgz#23f6a5cb9cb55315912cbec4ff7b0f59bbd191e2"
  integrity sha1-I/aly5y1UxWRLL7E/3sPWbvRkeI=
  dependencies:
    node-domexception "1.0.0"
    web-streams-polyfill "4.0.0-beta.3"

get-stream@^6.0.0, get-stream@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

gifwrap@^0.9.2:
  version "0.9.4"
  resolved "http://r.npm.sankuai.com/gifwrap/download/gifwrap-0.9.4.tgz#f4eb6169ba027d61df64aafbdcb1f8ae58ccc0c5"
  integrity sha1-9OthaboCfWHfZKr73LH4rljMwMU=
  dependencies:
    image-q "^4.0.0"
    omggif "^1.0.10"

global@~4.4.0:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/global/download/global-4.4.0.tgz#3e7b105179006a323ed71aafca3e9c57a5cc6406"
  integrity sha1-PnsQUXkAajI+1xqvyj6cV6XMZAY=
  dependencies:
    min-document "^2.19.0"
    process "^0.11.10"

got@^13.0.0:
  version "13.0.0"
  resolved "http://r.npm.sankuai.com/got/download/got-13.0.0.tgz#a2402862cef27a5d0d1b07c0fb25d12b58175422"
  integrity sha1-okAoYs7yel0NGwfA+yXRK1gXVCI=
  dependencies:
    "@sindresorhus/is" "^5.2.0"
    "@szmarczak/http-timer" "^5.0.1"
    cacheable-lookup "^7.0.0"
    cacheable-request "^10.2.8"
    decompress-response "^6.0.0"
    form-data-encoder "^2.1.2"
    get-stream "^6.0.1"
    http2-wrapper "^2.1.10"
    lowercase-keys "^3.0.0"
    p-cancelable "^3.0.0"
    responselike "^3.0.0"

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

http-cache-semantics@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/http-cache-semantics/download/http-cache-semantics-4.1.1.tgz#abe02fcb2985460bf0323be664436ec3476a6d5a"
  integrity sha1-q+AvyymFRgvwMjvmZENuw0dqbVo=

http2-wrapper@^2.1.10:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/http2-wrapper/download/http2-wrapper-2.2.0.tgz#b80ad199d216b7d3680195077bd7b9060fa9d7f3"
  integrity sha1-uArRmdIWt9NoAZUHe9e5Bg+p1/M=
  dependencies:
    quick-lru "^5.1.1"
    resolve-alpn "^1.2.0"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/human-signals/download/human-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/humanize-ms/download/humanize-ms-1.2.1.tgz#c46e3159a293f6b896da29316d8b6fe8bb79bbed"
  integrity sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=
  dependencies:
    ms "^2.0.0"

ieee754@^1.1.13, ieee754@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/ieee754/download/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

image-q@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/image-q/download/image-q-4.0.0.tgz#31e075be7bae3c1f42a85c469b4732c358981776"
  integrity sha1-MeB1vnuuPB9CqFxGm0cyw1iYF3Y=
  dependencies:
    "@types/node" "16.9.1"

immediate@~3.0.5:
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/immediate/download/immediate-3.0.6.tgz#9db1dbd0faf8de6fbe0f5dd5e56bb606280de69b"
  integrity sha1-nbHb0Pr43m++D13V5Wu2BigN5ps=

inherits@^2.0.3, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

is-buffer@~1.1.6:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-function@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-function/download/is-function-1.0.2.tgz#4f097f30abf6efadac9833b17ca5dc03f8144e08"
  integrity sha1-Twl/MKv2762smDOxfKXcA/gUTgg=

is-stream@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-stream/download/is-stream-2.0.1.tgz#fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

isarray@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

iterm2-version@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/iterm2-version/download/iterm2-version-5.0.0.tgz#cf714a55e0a541450e2b730bb56d79ebd1ae066a"
  integrity sha1-z3FKVeClQUUOK3MLtW1569GuBmo=
  dependencies:
    app-path "^4.0.0"
    plist "^3.0.2"

jimp@^0.14.0:
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/jimp/download/jimp-0.14.0.tgz#fde55f69bdb918c1b01ac633d89a25853af85625"
  integrity sha1-/eVfab25GMGwGsYz2JolhTr4ViU=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/custom" "^0.14.0"
    "@jimp/plugins" "^0.14.0"
    "@jimp/types" "^0.14.0"
    regenerator-runtime "^0.13.3"

jimp@^0.16.1:
  version "0.16.13"
  resolved "http://r.npm.sankuai.com/jimp/download/jimp-0.16.13.tgz#944b6368183235afc5d077429e2a7f34834acb18"
  integrity sha1-lEtjaBgyNa/F0HdCnip/NINKyxg=
  dependencies:
    "@babel/runtime" "^7.7.2"
    "@jimp/custom" "^0.16.13"
    "@jimp/plugins" "^0.16.13"
    "@jimp/types" "^0.16.13"
    regenerator-runtime "^0.13.3"

jpeg-js@^0.4.0, jpeg-js@^0.4.2:
  version "0.4.4"
  resolved "http://r.npm.sankuai.com/jpeg-js/download/jpeg-js-0.4.4.tgz#a9f1c6f1f9f0fa80cdb3484ed9635054d28936aa"
  integrity sha1-qfHG8fnw+oDNs0hO2WNQVNKJNqo=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz#9338802a30d3b6605fbe0613e094008ca8c05a13"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json5@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/json5/download/json5-1.0.2.tgz#63d98d60f21b313b77c4d6da18bfa69d80e1d593"
  integrity sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=
  dependencies:
    minimist "^1.2.0"

jszip@^3.7.1:
  version "3.10.1"
  resolved "http://r.npm.sankuai.com/jszip/download/jszip-3.10.1.tgz#34aee70eb18ea1faec2f589208a157d1feb091c2"
  integrity sha1-NK7nDrGOofrsL1iSCKFX0f6wkcI=
  dependencies:
    lie "~3.3.0"
    pako "~1.0.2"
    readable-stream "~2.3.6"
    setimmediate "^1.0.5"

keyv@^4.5.3:
  version "4.5.3"
  resolved "http://r.npm.sankuai.com/keyv/download/keyv-4.5.3.tgz#00873d2b046df737963157bd04f294ca818c9c25"
  integrity sha1-AIc9KwRt9zeWMVe9BPKUyoGMnCU=
  dependencies:
    json-buffer "3.0.1"

lie@~3.3.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/lie/download/lie-3.3.0.tgz#dcf82dee545f46074daf200c7c1c5a08e0f40f6a"
  integrity sha1-3Pgt7lRfRgdNryAMfBxaCOD0D2o=
  dependencies:
    immediate "~3.0.5"

load-bmfont@^1.3.1, load-bmfont@^1.4.0:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/load-bmfont/download/load-bmfont-1.4.1.tgz#c0f5f4711a1e2ccff725a7b6078087ccfcddd3e9"
  integrity sha1-wPX0cRoeLM/3Jae2B4CHzPzd0+k=
  dependencies:
    buffer-equal "0.0.1"
    mime "^1.3.4"
    parse-bmfont-ascii "^1.0.3"
    parse-bmfont-binary "^1.0.5"
    parse-bmfont-xml "^1.1.4"
    phin "^2.9.1"
    xhr "^2.0.1"
    xtend "^4.0.0"

loader-utils@^1.0.0:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/loader-utils/download/loader-utils-1.4.2.tgz#29a957f3a63973883eb684f10ffd3d151fec01a3"
  integrity sha1-KalX86Y5c4g+toTxD/09FR/sAaM=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

log-update@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/log-update/download/log-update-4.0.0.tgz#589ecd352471f2a1c0c570287543a64dfd20e0a1"
  integrity sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE=
  dependencies:
    ansi-escapes "^4.3.0"
    cli-cursor "^3.1.0"
    slice-ansi "^4.0.0"
    wrap-ansi "^6.2.0"

lop@^0.4.2:
  version "0.4.2"
  resolved "http://r.npm.sankuai.com/lop/download/lop-0.4.2.tgz#c9c2f958a39b9da1c2f36ca9ad66891a9fe84640"
  integrity sha1-ycL5WKObnaHC82yprWaJGp/oRkA=
  dependencies:
    duck "^0.1.12"
    option "~0.2.1"
    underscore "^1.13.1"

lowercase-keys@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/lowercase-keys/download/lowercase-keys-3.0.0.tgz#c5e7d442e37ead247ae9db117a9d0a467c89d4f2"
  integrity sha1-xefUQuN+rSR66dsRep0KRnyJ1PI=

mammoth@^1.9.1:
  version "1.9.1"
  resolved "http://r.npm.sankuai.com/mammoth/download/mammoth-1.9.1.tgz#b544c26747a412b5b00a11aa80477c6796860eaf"
  integrity sha1-tUTCZ0ekErWwChGqgEd8Z5aGDq8=
  dependencies:
    "@xmldom/xmldom" "^0.8.6"
    argparse "~1.0.3"
    base64-js "^1.5.1"
    bluebird "~3.4.0"
    dingbat-to-unicode "^1.0.1"
    jszip "^3.7.1"
    lop "^0.4.2"
    path-is-absolute "^1.0.0"
    underscore "^1.13.1"
    xmlbuilder "^10.0.0"

md5@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/md5/download/md5-2.3.0.tgz#c3da9a6aae3a30b46b7b0c349b87b110dc3bda4f"
  integrity sha1-w9qaaq46MLRreww0m4exENw72k8=
  dependencies:
    charenc "0.0.2"
    crypt "0.0.2"
    is-buffer "~1.1.6"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-types@^2.1.12:
  version "2.1.35"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime@^1.3.4:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/mime/download/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mimic-response@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/mimic-response/download/mimic-response-3.1.0.tgz#2d1d59af9c1b129815accc2c46a022a5ce1fa3c9"
  integrity sha1-LR1Zr5wbEpgVrMwsRqAipc4fo8k=

mimic-response@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/mimic-response/download/mimic-response-4.0.0.tgz#35468b19e7c75d10f5165ea25e75a5ceea7cf70f"
  integrity sha1-NUaLGefHXRD1Fl6iXnWlzup89w8=

min-document@^2.19.0:
  version "2.19.0"
  resolved "http://r.npm.sankuai.com/min-document/download/min-document-2.19.0.tgz#7bd282e3f5842ed295bb748cdd9f1ffa2c824685"
  integrity sha1-e9KC4/WELtKVu3SM3Z8f+iyCRoU=
  dependencies:
    dom-walk "^0.1.0"

minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

mkdirp@^0.5.1:
  version "0.5.6"
  resolved "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  integrity sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=
  dependencies:
    minimist "^1.2.6"

ms@^2.0.0:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

node-domexception@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/node-domexception/download/node-domexception-1.0.0.tgz#6888db46a1f71c0b76b3f7555016b63fe64766e5"
  integrity sha1-aIjbRqH3HAt2s/dVUBa2P+ZHZuU=

node-ensure@^0.0.0:
  version "0.0.0"
  resolved "http://r.npm.sankuai.com/node-ensure/download/node-ensure-0.0.0.tgz#ecae764150de99861ec5c810fd5d096b183932a7"
  integrity sha1-7K52QVDemYYexcgQ/V0Jaxg5Mqc=

node-fetch@^2.6.7:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/node-fetch/download/node-fetch-2.7.0.tgz#d0f0fa6e3e2dc1d27efcd8ad99d550bda94d187d"
  integrity sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=
  dependencies:
    whatwg-url "^5.0.0"

node-gyp-build@^4.3.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/node-gyp-build/download/node-gyp-build-4.6.0.tgz#0c52e4cbf54bbd28b709820ef7b6a3c2d6209055"
  integrity sha1-DFLky/VLvSi3CYIO97ajwtYgkFU=

normalize-url@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/normalize-url/download/normalize-url-8.0.0.tgz#593dbd284f743e8dcf6a5ddf8fadff149c82701a"
  integrity sha1-WT29KE90Po3Pal3fj63/FJyCcBo=

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

omggif@^1.0.10, omggif@^1.0.9:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/omggif/download/omggif-1.0.10.tgz#ddaaf90d4a42f532e9e7cb3a95ecdd47f17c7b19"
  integrity sha1-3ar5DUpC9TLp58s6lezdR/F8exk=

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

openai@3.3.0, openai@^3.2.1:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/openai/download/openai-3.3.0.tgz#a6408016ad0945738e1febf43f2fccca83a3f532"
  integrity sha1-pkCAFq0JRXOOH+v0Py/MyoOj9TI=
  dependencies:
    axios "^0.26.0"
    form-data "^4.0.0"

option@~0.2.1:
  version "0.2.4"
  resolved "http://r.npm.sankuai.com/option/download/option-0.2.4.tgz#fd475cdf98dcabb3cb397a3ba5284feb45edbfe4"
  integrity sha1-/Udc35jcq7PLOXo7pShP60Xtv+Q=

p-cancelable@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/p-cancelable/download/p-cancelable-3.0.0.tgz#63826694b54d61ca1c20ebcb6d3ecf5e14cd8050"
  integrity sha1-Y4JmlLVNYcocIOvLbT7PXhTNgFA=

pako@^1.0.5, pako@~1.0.2:
  version "1.0.11"
  resolved "http://r.npm.sankuai.com/pako/download/pako-1.0.11.tgz#6c9599d340d54dfd3946380252a35705a6b992bf"
  integrity sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=

parse-bmfont-ascii@^1.0.3:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/parse-bmfont-ascii/download/parse-bmfont-ascii-1.0.6.tgz#11ac3c3ff58f7c2020ab22769079108d4dfa0285"
  integrity sha1-Eaw8P/WPfCAgqyJ2kHkQjU36AoU=

parse-bmfont-binary@^1.0.5:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/parse-bmfont-binary/download/parse-bmfont-binary-1.0.6.tgz#d038b476d3e9dd9db1e11a0b0e53a22792b69006"
  integrity sha1-0Di0dtPp3Z2x4RoLDlOiJ5K2kAY=

parse-bmfont-xml@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/parse-bmfont-xml/download/parse-bmfont-xml-1.1.4.tgz#015319797e3e12f9e739c4d513872cd2fa35f389"
  integrity sha1-AVMZeX4+EvnnOcTVE4cs0vo184k=
  dependencies:
    xml-parse-from-string "^1.0.0"
    xml2js "^0.4.5"

parse-headers@^2.0.0:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/parse-headers/download/parse-headers-2.0.5.tgz#069793f9356a54008571eb7f9761153e6c770da9"
  integrity sha1-BpeT+TVqVACFcet/l2EVPmx3Dak=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

pdf-ts@^0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/pdf-ts/download/pdf-ts-0.0.2.tgz#27fc7842f998fdd679ae1aa2b95e42b8e0736758"
  integrity sha1-J/x4QvmY/dZ5rhqiuV5CuOBzZ1g=
  dependencies:
    pdfjs-dist "1.10.100"

pdfjs-dist@1.10.100:
  version "1.10.100"
  resolved "http://r.npm.sankuai.com/pdfjs-dist/download/pdfjs-dist-1.10.100.tgz#d5a250b42482ab6e41d763a795ce7cdebe6b1894"
  integrity sha1-1aJQtCSCq25B12Onlc583r5rGJQ=
  dependencies:
    node-ensure "^0.0.0"
    worker-loader "^1.0.0"

peek-readable@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/peek-readable/download/peek-readable-4.1.0.tgz#4ece1111bf5c2ad8867c314c81356847e8a62e72"
  integrity sha1-Ts4REb9cKtiGfDFMgTVoR+imLnI=

phin@^2.9.1:
  version "2.9.3"
  resolved "http://r.npm.sankuai.com/phin/download/phin-2.9.3.tgz#f9b6ac10a035636fb65dfc576aaaa17b8743125c"
  integrity sha1-+basEKA1Y2+2XfxXaqqhe4dDElw=

pixelmatch@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/pixelmatch/download/pixelmatch-4.0.2.tgz#8f47dcec5011b477b67db03c243bc1f3085e8854"
  integrity sha1-j0fc7FARtHe2fbA8JDvB8wheiFQ=
  dependencies:
    pngjs "^3.0.0"

plist@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/plist/download/plist-3.1.0.tgz#797a516a93e62f5bde55e0b9cc9c967f860893c9"
  integrity sha1-eXpRapPmL1veVeC5zJyWf4YIk8k=
  dependencies:
    "@xmldom/xmldom" "^0.8.8"
    base64-js "^1.5.1"
    xmlbuilder "^15.1.1"

pngjs@^3.0.0, pngjs@^3.3.3:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/pngjs/download/pngjs-3.4.0.tgz#99ca7d725965fb655814eaf65f38f12bbdbf555f"
  integrity sha1-mcp9clll+2VYFOr2XzjxK72/VV8=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "http://r.npm.sankuai.com/process/download/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

punycode@^2.1.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-2.3.0.tgz#f67fa67c94da8f4d0cfff981aee4118064199b8f"
  integrity sha1-9n+mfJTaj00M//mBruQRgGQZm48=

quick-lru@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/quick-lru/download/quick-lru-5.1.1.tgz#366493e6b3e42a3a6885e2e99d18f80fb7a8c932"
  integrity sha1-NmST5rPkKjpoheLpnRj4D7eoyTI=

readable-stream@^3.6.0:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz#56a9b36ea965c00c5a93ef31eb111a0f11056967"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~2.3.6:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz#91125e8042bba1b9887f49345f6277027ce8be9b"
  integrity sha1-kRJegEK7obmIf0k0X2J3Anzovps=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-web-to-node-stream@^3.0.0:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/readable-web-to-node-stream/download/readable-web-to-node-stream-3.0.2.tgz#5d52bb5df7b54861fd48d015e93a2cb87b3ee0bb"
  integrity sha1-XVK7Xfe1SGH9SNAV6TosuHs+4Ls=
  dependencies:
    readable-stream "^3.6.0"

regenerator-runtime@^0.13.3:
  version "0.13.11"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.13.11.tgz#f6dca3e7ceec20590d07ada785636a90cdca17f9"
  integrity sha1-9tyj587sIFkNB62nhWNqkM3KF/k=

regenerator-runtime@^0.14.0:
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.14.0.tgz#5e19d68eb12d486f797e15a3c6a918f7cec5eb45"
  integrity sha1-XhnWjrEtSG95fhWjxqkY987F60U=

render-gif@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/render-gif/download/render-gif-2.0.4.tgz#25b4afa8a1b668c21d06b715e96338fd24f47c69"
  integrity sha1-JbSvqKG2aMIdBrcV6WM4/ST0fGk=
  dependencies:
    cycled "^1.2.0"
    decode-gif "^1.0.1"
    delay "^4.3.0"
    jimp "^0.14.0"

resolve-alpn@^1.2.0:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/resolve-alpn/download/resolve-alpn-1.2.1.tgz#b7adbdac3546aaaec20b45e7d8265927072726f9"
  integrity sha1-t629rDVGqq7CC0Xn2CZZJwcnJvk=

responselike@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/responselike/download/responselike-3.0.0.tgz#20decb6c298aff0dbee1c355ca95461d42823626"
  integrity sha1-IN7LbCmK/w2+4cNVypVGHUKCNiY=
  dependencies:
    lowercase-keys "^3.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

sax@>=0.6.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/sax/download/sax-1.3.0.tgz#a5dbe77db3be05c9d1ee7785dbd3ea9de51593d0"
  integrity sha1-pdvnfbO+BcnR7neF29PqneUVk9A=

schema-utils@^0.4.0:
  version "0.4.7"
  resolved "http://r.npm.sankuai.com/schema-utils/download/schema-utils-0.4.7.tgz#ba74f597d2be2ea880131746ee17d0a093c68187"
  integrity sha1-unT1l9K+LqiAExdG7hfQoJPGgYc=
  dependencies:
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"

setimmediate@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/setimmediate/download/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

signal-exit@^3.0.2, signal-exit@^3.0.3:
  version "3.0.7"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-4.0.0.tgz#500e8dd0fd55b05815086255b3195adf2a45fe6b"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

string-width@^4.1.0:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strtok3@^6.2.4:
  version "6.3.0"
  resolved "http://r.npm.sankuai.com/strtok3/download/strtok3-6.3.0.tgz#358b80ffe6d5d5620e19a073aa78ce947a90f9a0"
  integrity sha1-NYuA/+bV1WIOGaBzqnjOlHqQ+aA=
  dependencies:
    "@tokenizer/token" "^0.3.0"
    peek-readable "^4.1.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

term-img@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/term-img/download/term-img-6.0.0.tgz#c3816f84bcca724a9ca14045dc95f984e286f074"
  integrity sha1-w4FvhLzKckqcoUBF3JX5hOKG8HQ=
  dependencies:
    ansi-escapes "^5.0.0"
    iterm2-version "^5.0.0"

terminal-image@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/terminal-image/download/terminal-image-2.0.0.tgz#d4d80fa7ac99fcb122910dcf007234e7aa66bb77"
  integrity sha1-1NgPp6yZ/LEikQ3PAHI056pmu3c=
  dependencies:
    chalk "^4.1.1"
    jimp "^0.16.1"
    log-update "^4.0.0"
    render-gif "^2.0.4"
    term-img "^6.0.0"

timm@^1.6.1:
  version "1.7.1"
  resolved "http://r.npm.sankuai.com/timm/download/timm-1.7.1.tgz#96bab60c7d45b5a10a8a4d0f0117c6b7e5aff76f"
  integrity sha1-lrq2DH1FtaEKik0PARfGt+Wv928=

tinycolor2@^1.4.1:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/tinycolor2/download/tinycolor2-1.6.0.tgz#f98007460169b0263b97072c5ae92484ce02d09e"
  integrity sha1-+YAHRgFpsCY7lwcsWukkhM4C0J4=

token-types@^4.1.1:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/token-types/download/token-types-4.2.1.tgz#0f897f03665846982806e138977dbe72d44df753"
  integrity sha1-D4l/A2ZYRpgoBuE4l32+ctRN91M=
  dependencies:
    "@tokenizer/token" "^0.3.0"
    ieee754 "^1.2.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/tr46/download/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.21.3.tgz#d260a24b0198436e133fa26a524a6d65fa3b2e37"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^1.0.2:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-1.4.0.tgz#e9fb813fe3bf1744ec359d55d1affefa76f14be1"
  integrity sha1-6fuBP+O/F0TsNZ1V0a/++nbxS+E=

underscore@^1.13.1:
  version "1.13.7"
  resolved "http://r.npm.sankuai.com/underscore/download/underscore-1.13.7.tgz#970e33963af9a7dda228f17ebe8399e5fbe63a10"
  integrity sha1-lw4zljr5p92iKPF+voOZ5fvmOhA=

undici-types@~5.25.1:
  version "5.25.3"
  resolved "http://r.npm.sankuai.com/undici-types/download/undici-types-5.25.3.tgz#e044115914c85f0bcbb229f346ab739f064998c3"
  integrity sha1-4EQRWRTIXwvLsinzRqtznwZJmMM=

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

utf-8-validate@6.0.3:
  version "6.0.3"
  resolved "http://r.npm.sankuai.com/utf-8-validate/download/utf-8-validate-6.0.3.tgz#7d8c936d854e86b24d1d655f138ee27d2636d777"
  integrity sha1-fYyTbYVOhrJNHWVfE47ifSY213c=
  dependencies:
    node-gyp-build "^4.3.0"

utif@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/utif/download/utif-2.0.1.tgz#9e1582d9bbd20011a6588548ed3266298e711759"
  integrity sha1-nhWC2bvSABGmWIVI7TJmKY5xF1k=
  dependencies:
    pako "^1.0.5"

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

web-streams-polyfill@4.0.0-beta.3:
  version "4.0.0-beta.3"
  resolved "http://r.npm.sankuai.com/web-streams-polyfill/download/web-streams-polyfill-4.0.0-beta.3.tgz#2898486b74f5156095e473efe989dcf185047a38"
  integrity sha1-KJhIa3T1FWCV5HPv6Ync8YUEejg=

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

worker-loader@^1.0.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/worker-loader/download/worker-loader-1.1.1.tgz#920d74ddac6816fc635392653ed8b4af1929fd92"
  integrity sha1-kg103axoFvxjU5JlPti0rxkp/ZI=
  dependencies:
    loader-utils "^1.0.0"
    schema-utils "^0.4.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

ws@8.13.0:
  version "8.13.0"
  resolved "http://r.npm.sankuai.com/ws/download/ws-8.13.0.tgz#9a9fb92f93cf41512a0735c8f4dd09b8a1211cd0"
  integrity sha1-mp+5L5PPQVEqBzXI9N0JuKEhHNA=

xhr@^2.0.1:
  version "2.6.0"
  resolved "http://r.npm.sankuai.com/xhr/download/xhr-2.6.0.tgz#b69d4395e792b4173d6b7df077f0fc5e4e2b249d"
  integrity sha1-tp1DleeStBc9a33wd/D8Xk4rJJ0=
  dependencies:
    global "~4.4.0"
    is-function "^1.0.1"
    parse-headers "^2.0.0"
    xtend "^4.0.0"

xml-parse-from-string@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/xml-parse-from-string/download/xml-parse-from-string-1.0.1.tgz#a9029e929d3dbcded169f3c6e28238d95a5d5a28"
  integrity sha1-qQKekp09vN7RafPG4oI42VpdWig=

xml2js@^0.4.5:
  version "0.4.23"
  resolved "http://r.npm.sankuai.com/xml2js/download/xml2js-0.4.23.tgz#a0c69516752421eb2ac758ee4d4ccf58843eac66"
  integrity sha1-oMaVFnUkIesqx1juTUzPWIQ+rGY=
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@^10.0.0:
  version "10.1.1"
  resolved "http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-10.1.1.tgz#8cae6688cc9b38d850b7c8d3c0a4161dcaf475b0"
  integrity sha1-jK5miMybONhQt8jTwKQWHcr0dbA=

xmlbuilder@^15.1.1:
  version "15.1.1"
  resolved "http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-15.1.1.tgz#9dcdce49eea66d8d10b42cae94a79c3c8d0c2ec5"
  integrity sha1-nc3OSe6mbY0QtCyulKecPI0MLsU=

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-11.0.1.tgz#be9bae1c8a046e76b31127726347d0ad7002beb3"
  integrity sha1-vpuuHIoEbnazESdyY0fQrXACvrM=

xtend@^4.0.0:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=
